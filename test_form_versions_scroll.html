<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单版本滚动测试</title>
    <link rel="stylesheet" href="static/css/conversation_styles.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            padding-left: 280px;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-info h2 {
            margin-top: 0;
            color: #333;
        }
        
        .test-info ul {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <!-- 测试用的侧边栏 -->
    <div class="unified-sidebar" id="unified-sidebar">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
            <button class="sidebar-toggle-btn" id="sidebar-toggle-btn">
                <span class="toggle-icon">⊞</span>
            </button>
        </div>

        <!-- 新建对话按钮 -->
        <div class="new-conversation-section">
            <button class="new-conversation-btn">
                <span class="btn-icon">+</span>
                <span class="btn-text">新对话</span>
            </button>
        </div>

        <!-- 对话历史列表 -->
        <div class="conversation-history-section">
            <div class="section-title">历史对话</div>
            <div class="conversation-list" id="conversation-list">
                <!-- 添加多个对话项用于测试滚动 -->
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-content">
                        <div class="conversation-title">测试对话 1</div>
                        <div class="conversation-timestamp">07/23 10:30</div>
                    </div>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-content">
                        <div class="conversation-title">测试对话 2</div>
                        <div class="conversation-timestamp">07/23 11:15</div>
                    </div>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-content">
                        <div class="conversation-title">测试对话 3</div>
                        <div class="conversation-timestamp">07/23 12:00</div>
                    </div>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-content">
                        <div class="conversation-title">测试对话 4</div>
                        <div class="conversation-timestamp">07/23 13:45</div>
                    </div>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-content">
                        <div class="conversation-title">测试对话 5</div>
                        <div class="conversation-timestamp">07/23 14:20</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单版本管理区域 -->
        <div class="form-versions-area" id="form-versions-area">
            <div class="section-title">表单版本</div>
            <div class="form-version-actions">
                <button class="create-version-btn" id="create-version-btn">
                    <span class="btn-icon">+</span>
                    <span class="btn-text">新增版本</span>
                </button>
            </div>
            <div class="versions-container" id="versions-container">
                <!-- 添加多个版本项用于测试滚动 -->
                <div class="form-version-item current-version">
                    <div class="form-version-icon">🤖</div>
                    <div class="form-version-content">
                        <div class="form-version-title">AI优化版本 #10</div>
                        <div class="form-version-timestamp">07/23 17:20</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">👤</div>
                    <div class="form-version-content">
                        <div class="form-version-title">用户版本 #9</div>
                        <div class="form-version-timestamp">07/23 17:15</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">🤖</div>
                    <div class="form-version-content">
                        <div class="form-version-title">AI优化版本 #8</div>
                        <div class="form-version-timestamp">07/23 17:10</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">👤</div>
                    <div class="form-version-content">
                        <div class="form-version-title">用户版本 #7</div>
                        <div class="form-version-timestamp">07/23 17:05</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">🤖</div>
                    <div class="form-version-content">
                        <div class="form-version-title">AI优化版本 #6</div>
                        <div class="form-version-timestamp">07/23 17:00</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">👤</div>
                    <div class="form-version-content">
                        <div class="form-version-title">用户版本 #5</div>
                        <div class="form-version-timestamp">07/23 16:55</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">🤖</div>
                    <div class="form-version-content">
                        <div class="form-version-title">AI优化版本 #4</div>
                        <div class="form-version-timestamp">07/23 16:50</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">👤</div>
                    <div class="form-version-content">
                        <div class="form-version-title">用户版本 #3</div>
                        <div class="form-version-timestamp">07/23 16:45</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">🤖</div>
                    <div class="form-version-content">
                        <div class="form-version-title">AI优化版本 #2</div>
                        <div class="form-version-timestamp">07/23 16:40</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
                <div class="form-version-item">
                    <div class="form-version-icon">👤</div>
                    <div class="form-version-content">
                        <div class="form-version-title">初始版本 #1</div>
                        <div class="form-version-timestamp">07/23 16:35</div>
                    </div>
                    <button class="form-version-remove-btn">×</button>
                </div>
            </div>
        </div>

        <!-- 侧边栏底部操作按钮 -->
        <div class="sidebar-bottom-actions">
            <button type="button" class="sidebar-action-btn ai-optimize-btn" id="ai-optimize-btn">
                <span class="btn-icon">🤖</span>
                <span class="btn-text">AI优化</span>
            </button>
            <button type="button" class="sidebar-action-btn direct-generate-btn" id="direct-generate-btn">
                <span class="btn-icon">📄</span>
                <span class="btn-text">直接生成报告</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="test-container">
        <div class="test-info">
            <h2>表单版本滚动测试</h2>
            <p>这个页面用于测试重新设计的表单版本区域的滚动功能。</p>
            <h3>测试项目：</h3>
            <ul>
                <li>表单版本区域是否显示滚动条</li>
                <li>鼠标悬停在表单版本区域时，滚轮是否能正常滚动</li>
                <li>滚动条样式是否正确显示</li>
                <li>滚动是否平滑</li>
                <li>新增版本按钮是否正确显示</li>
                <li>侧边栏底部按钮是否正确显示</li>
            </ul>
            <h3>预期行为：</h3>
            <ul>
                <li>表单版本区域应该显示细滚动条（宽度4px）</li>
                <li>表单版本区域使用固定高度（35%）</li>
                <li>鼠标悬停在表单版本区域时，滚轮应该能够滚动版本列表</li>
                <li>滚动条应该在鼠标悬停时变色</li>
                <li>滚动应该限制在表单版本区域内，不影响主页面</li>
                <li>新增版本按钮应该显示在表单版本区域顶部</li>
                <li>AI优化和直接生成报告按钮应该显示在侧边栏最下方</li>
                <li>所有变量名和ID都已更新，避免与旧代码冲突</li>
            </ul>
        </div>
    </div>

    <script src="static/js/d8_form.js"></script>
    <script>
        // 初始化滚轮控制
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof initWheelControl === 'function') {
                initWheelControl();
            }
        });
    </script>
</body>
</html>
