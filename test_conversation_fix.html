<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试创建新对话修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .log-success { color: #28a745; }
        .log-warning { color: #ffc107; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>创建新对话功能修复测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>这个页面用于测试修复后的创建新对话功能。修复的主要问题：</p>
        <ul>
            <li>修复了表单清空失败导致的误导性错误提示</li>
            <li>改善了错误处理逻辑，只有在真正失败时才显示错误</li>
            <li>添加了更好的成功提示和Toast通知</li>
            <li>增强了各个函数的错误处理能力</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>测试操作</h2>
        <button class="test-button" onclick="testCreateConversation()">测试创建新对话</button>
        <button class="test-button" onclick="testClearForm()">测试清空表单</button>
        <button class="test-button" onclick="testErrorHandling()">测试错误处理</button>
        <button class="test-button" onclick="clearLogs()">清空日志</button>
        
        <div id="status" class="status info">
            准备就绪，点击按钮开始测试
        </div>
    </div>

    <div class="test-container">
        <h2>测试日志</h2>
        <div id="log-container" class="log-container">
            <div class="log-entry log-info">测试页面已加载，等待测试...</div>
        </div>
    </div>

    <!-- 模拟表单元素 -->
    <div style="display: none;">
        <form id="d8-form">
            <input type="text" id="d0_background" name="d0_background" />
            <textarea id="d2_description" name="d2_description"></textarea>
            <div id="dynamic-members-container"></div>
            <div id="dynamic-measures-container"></div>
            <div id="dynamic-d5-measures-container"></div>
            <div id="dynamic-d6-verifications-container"></div>
            <div id="dynamic-d7-preventions-container"></div>
            <span id="member-count">3</span>
        </form>
        
        <!-- 模拟章节元素 -->
        <div id="section-d0">
            <input type="text" id="d0_title" />
            <input type="text" id="d0_reporter" />
            <input type="text" id="d0_time" />
            <textarea id="d0_background_section"></textarea>
        </div>
        <div id="section-d2">
            <textarea id="d2_description_section"></textarea>
            <input type="text" id="d2_when" />
            <input type="text" id="d2_where" />
        </div>
        
        <!-- 模拟进度元素 -->
        <span id="completion-d0">0/4</span>
        <span id="completion-d2">0/8</span>
        <span id="nav-progress-d0">0/4</span>
        <span id="nav-progress-d2">0/8</span>
    </div>

    <script>
        // 模拟必要的全局变量和函数
        window.isFormClearing = false;
        
        // 日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
            addLog('日志已清空');
        }
        
        // 模拟对话管理器
        class MockConversationManager {
            constructor() {
                this.conversations = new Map();
                this.currentConversation = null;
            }
            
            generateId() {
                return 'conv_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
            }
            
            createConversation(initialFormData = null, title = null) {
                const conversationId = this.generateId();
                const now = new Date().toISOString();
                
                const conversation = {
                    conversation_id: conversationId,
                    title: title || '新建8D报告',
                    created_at: now,
                    updated_at: now,
                    status: 'active',
                    form_versions: [],
                    chat_history: [],
                    generated_documents: [],
                    session_id: null
                };
                
                this.conversations.set(conversationId, conversation);
                this.currentConversation = conversation;
                
                addLog(`创建对话成功: ${conversationId}`, 'success');
                return conversationId;
            }
            
            addFormVersion(conversation, formData, source, description) {
                const versionId = this.generateId();
                const version = {
                    version_id: versionId,
                    form_data: formData,
                    source: source,
                    description: description,
                    timestamp: new Date().toISOString()
                };
                
                conversation.form_versions.push(version);
                addLog(`添加表单版本成功: ${versionId}`, 'success');
                return versionId;
            }
        }
        
        // 模拟必要的函数
        function updateMemberCount() {
            addLog('updateMemberCount 调用成功');
        }
        
        function updateMeasureCount() {
            addLog('updateMeasureCount 调用成功');
        }
        
        function updateD5MeasureCount() {
            addLog('updateD5MeasureCount 调用成功');
        }
        
        function updateD6VerificationCount() {
            addLog('updateD6VerificationCount 调用成功');
        }
        
        function updateD7PreventionCount() {
            addLog('updateD7PreventionCount 调用成功');
        }
        
        function clearValidationErrors() {
            addLog('clearValidationErrors 调用成功');
        }
        
        function autoResizeTextarea(textarea) {
            addLog('autoResizeTextarea 调用成功');
        }
        
        // 章节字段配置
        const sectionFields = {
            'guide': [],
            'd0': ['d0_title', 'd0_reporter', 'd0_time', 'd0_background'],
            'd1': [],
            'd2': ['d2_description', 'd2_when', 'd2_where', 'd2_who', 'd2_what', 'd2_why', 'd2_how', 'd2_impact'],
            'd3': []
        };
        
        // 初始化模拟环境
        window.conversationManager = new MockConversationManager();
        
        addLog('模拟环境初始化完成', 'success');
    </script>
    
    <!-- 加载修复后的代码 -->
    <script src="static/js/d8_form.js"></script>
    <script src="static/js/conversation_manager.js"></script>
    <script src="static/js/conversation_ui.js"></script>
    
    <script>
        // 测试函数
        function testCreateConversation() {
            addLog('开始测试创建新对话...', 'info');
            setStatus('正在测试创建新对话...', 'info');
            
            try {
                if (window.conversationUI) {
                    window.conversationUI.createNewConversation()
                        .then(() => {
                            addLog('创建新对话测试完成', 'success');
                            setStatus('创建新对话测试成功！', 'success');
                        })
                        .catch(error => {
                            addLog(`创建新对话测试失败: ${error.message}`, 'error');
                            setStatus('创建新对话测试失败', 'error');
                        });
                } else {
                    addLog('conversationUI 未初始化', 'error');
                    setStatus('conversationUI 未初始化', 'error');
                }
            } catch (error) {
                addLog(`测试异常: ${error.message}`, 'error');
                setStatus('测试异常', 'error');
            }
        }
        
        function testClearForm() {
            addLog('开始测试清空表单...', 'info');
            setStatus('正在测试清空表单...', 'info');
            
            try {
                if (typeof clearForm === 'function') {
                    clearForm();
                    addLog('清空表单测试成功', 'success');
                    setStatus('清空表单测试成功！', 'success');
                } else {
                    addLog('clearForm 函数不存在', 'warning');
                    setStatus('clearForm 函数不存在', 'warning');
                }
            } catch (error) {
                addLog(`清空表单测试失败: ${error.message}`, 'error');
                setStatus('清空表单测试失败', 'error');
            }
        }
        
        function testErrorHandling() {
            addLog('开始测试错误处理...', 'info');
            setStatus('正在测试错误处理...', 'info');
            
            try {
                // 模拟一个会导致错误的情况
                if (window.conversationUI) {
                    // 临时破坏一个函数来测试错误处理
                    const originalClearForm = window.clearForm;
                    window.clearForm = function() {
                        throw new Error('模拟的清空表单错误');
                    };
                    
                    window.conversationUI.createNewConversation()
                        .then(() => {
                            addLog('错误处理测试：对话仍然创建成功（符合预期）', 'success');
                            setStatus('错误处理测试成功！', 'success');
                            // 恢复原函数
                            window.clearForm = originalClearForm;
                        })
                        .catch(error => {
                            addLog(`错误处理测试失败: ${error.message}`, 'error');
                            setStatus('错误处理测试失败', 'error');
                            // 恢复原函数
                            window.clearForm = originalClearForm;
                        });
                } else {
                    addLog('conversationUI 未初始化', 'error');
                    setStatus('conversationUI 未初始化', 'error');
                }
            } catch (error) {
                addLog(`错误处理测试异常: ${error.message}`, 'error');
                setStatus('错误处理测试异常', 'error');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，开始初始化...', 'info');
            
            setTimeout(() => {
                if (window.conversationUI && window.conversationManager) {
                    addLog('对话系统初始化成功', 'success');
                    setStatus('系统就绪，可以开始测试', 'success');
                } else {
                    addLog('对话系统初始化失败', 'error');
                    setStatus('系统初始化失败', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
