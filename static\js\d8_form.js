// D8表单交互功能脚本

// 自动调整textarea高度的功能
function autoResizeTextarea(textarea) {
    // 保存当前的滚动位置
    const scrollTop = textarea.scrollTop;
    
    // 如果内容为空，直接设置为最小高度
    if (textarea.value.trim() === '') {
        textarea.style.height = '38px';
        return;
    }
    
    // 检查是否包含换行符
    const hasLineBreaks = textarea.value.includes('\n');
    
    // 临时设置高度为auto来测量内容高度
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    
    // 重新设置为最小高度来测试是否需要滚动
    textarea.style.height = '38px';
    const needsScroll = textarea.scrollHeight > 38;
    
    // 判断是否需要扩展高度
    if (hasLineBreaks || needsScroll) {
        // 如果有换行符或者内容超出单行，扩展高度
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    } else {
        // 否则保持单行高度
        textarea.style.height = '38px';
    }
    
    // 恢复滚动位置
    textarea.scrollTop = scrollTop;
}

// 初始化所有textarea的自动调整功能
function initAutoResize() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        // 设置初始高度
        textarea.style.height = '38px';
        
        // 监听输入事件
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        
        // 监听paste事件（粘贴）
        textarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        
        // 监听keyup事件（按键释放后）
        textarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        
        // 监听删除事件
        textarea.addEventListener('keydown', function(e) {
            // 在删除键按下后立即调整高度
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
        
        // 初始化时调整一次
        autoResizeTextarea(textarea);
    });
}

// 动态添加成员功能
// 不再需要全局计数器，每次添加时动态查找可用序号

// 动态添加措施功能
// 不再需要全局计数器，每次添加时动态查找可用序号

function addMember() {
    const container = document.getElementById('dynamic-members-container');
    
    // 找到下一个可用的序号
    let newIndex = 3; // 从3开始，因为1、2是固定成员
    while (document.getElementById(`member-group-${newIndex}`)) {
        newIndex++;
    }
    
                // 创建新成员的HTML
            const memberHTML = `
                <div class="member-group" id="member-group-${newIndex}">
                    <div class="group-title">
                        成员${newIndex}
                        <button type="button" class="remove-member-btn" onclick="removeMember(${newIndex})">
                            删除
                        </button>
                    </div>
                    <div class="form-row-4">
                        <div class="form-group">
                            <label for="d1_member${newIndex}_name">姓名</label>
                            <textarea id="d1_member${newIndex}_name" name="d1_member${newIndex}_name"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="d1_member${newIndex}_dept">部门</label>
                            <textarea id="d1_member${newIndex}_dept" name="d1_member${newIndex}_dept"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="d1_member${newIndex}_position">职位</label>
                            <textarea id="d1_member${newIndex}_position" name="d1_member${newIndex}_position"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="d1_member${newIndex}_responsibility">主要职责</label>
                            <textarea id="d1_member${newIndex}_responsibility" name="d1_member${newIndex}_responsibility" class="auto-resize-textarea"></textarea>
                        </div>
                    </div>
                </div>
            `;
    
    // 添加到容器中
    container.insertAdjacentHTML('beforeend', memberHTML);
    
    // 更新成员计数
    updateMemberCount();
    
    // 更新进度
    updateProgress();
    
    // 为新添加的textarea初始化自动调整高度功能
    const newTextareas = container.querySelectorAll(`#member-group-${newIndex} textarea`);
    newTextareas.forEach(textarea => {
        textarea.style.height = '38px';
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        textarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
    });
}

function removeMember(memberId) {
    const memberGroup = document.getElementById(`member-group-${memberId}`);
    if (memberGroup) {
        memberGroup.remove();
        // 删除后重新排序所有成员
        reorderMembers();
        updateMemberCount();
        // 更新进度
        updateProgress();
    }
}

// 重新排序成员功能
function reorderMembers() {
    const container = document.getElementById('dynamic-members-container');
    const members = Array.from(container.querySelectorAll('.member-group'));
    
    // 按当前序号排序
    members.sort((a, b) => {
        const aIndex = parseInt(a.id.split('-').pop());
        const bIndex = parseInt(b.id.split('-').pop());
        return aIndex - bIndex;
    });
    
    // 重新分配序号，从3开始（因为1、2是固定成员）
    members.forEach((memberElement, index) => {
        const newIndex = index + 3;
        const oldId = memberElement.id;
        const oldIndex = parseInt(oldId.split('-').pop());
        
        if (newIndex !== oldIndex) {
            // 更新容器ID
            memberElement.id = `member-group-${newIndex}`;
            
            // 更新组标题
            const groupTitle = memberElement.querySelector('.group-title');
            const titleText = groupTitle.childNodes[0].textContent.trim();
            groupTitle.childNodes[0].textContent = `成员${newIndex}`;
            
            // 更新删除按钮的onclick
            const removeBtn = memberElement.querySelector('.remove-member-btn');
            removeBtn.setAttribute('onclick', `removeMember(${newIndex})`);
            
            // 更新所有input字段的ID和name
            const fields = ['name', 'dept', 'position', 'responsibility'];
            fields.forEach(field => {
                const input = memberElement.querySelector(`[name*="_${field}"]`);
                const label = memberElement.querySelector(`label[for*="_${field}"]`);
                if (input && label) {
                    input.id = `d1_member${newIndex}_${field}`;
                    input.name = `d1_member${newIndex}_${field}`;
                    label.setAttribute('for', `d1_member${newIndex}_${field}`);
                }
            });
        }
    });
}

function updateMemberCount() {
    try {
        const dynamicMembers = document.querySelectorAll('#dynamic-members-container .member-group');
        const totalMembers = 3 + dynamicMembers.length; // 1个组长 + 2个固定成员 + 动态成员
        const memberCountElement = document.getElementById('member-count');
        if (memberCountElement) {
            memberCountElement.textContent = totalMembers;
        }
    } catch (error) {
        console.warn('updateMemberCount失败:', error);
    }
}

// 措施相关函数
function addMeasure() {
    const container = document.getElementById('dynamic-measures-container');
    
    // 找到下一个可用的序号
    let newIndex = 2; // 从2开始，因为1是固定措施
    while (document.getElementById(`measure-group-${newIndex}`)) {
        newIndex++;
    }
    
    // 创建新措施的HTML
    const measureHTML = `
        <div class="measure-group" id="measure-group-${newIndex}">
            <div class="group-title">
                临时措施${newIndex}
                <button type="button" class="remove-member-btn" onclick="removeMeasure(${newIndex})">
                    删除
                </button>
            </div>
            <!-- 前两个字段：范围和处置对策，处置对策更宽 -->
            <div class="form-row-d3-top">
                <div class="form-group">
                    <label for="d3_scope${newIndex}">范围</label>
                    <textarea id="d3_scope${newIndex}" name="d3_scope${newIndex}" class="auto-resize-textarea"></textarea>
                </div>
                <div class="form-group">
                    <label for="d3_measure${newIndex}">处置对策</label>
                    <textarea id="d3_measure${newIndex}" name="d3_measure${newIndex}" class="auto-resize-textarea"></textarea>
                </div>
            </div>
            <!-- 后四个字段：责任人、完成期限、状态、进度备注 -->
            <div class="form-row-4">
                <div class="form-group">
                    <label for="d3_responsible${newIndex}">责任人</label>
                    <textarea id="d3_responsible${newIndex}" name="d3_responsible${newIndex}"></textarea>
                </div>
                <div class="form-group">
                                                        <label for="d3_deadline${newIndex}">完成期限</label>
                                    <input type="date" id="d3_deadline${newIndex}" name="d3_deadline${newIndex}">
                </div>
                <div class="form-group">
                    <label for="d3_status${newIndex}">状态</label>
                    <select id="d3_status${newIndex}" name="d3_status${newIndex}">
                        <option value="">请选择</option>
                        <option value="已完成">已完成</option>
                        <option value="进行中">进行中</option>
                        <option value="未开始">未开始</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="d3_note${newIndex}">进度备注</label>
                    <textarea id="d3_note${newIndex}" name="d3_note${newIndex}"></textarea>
                </div>
            </div>
        </div>
    `;
    
    // 添加到容器中
    container.insertAdjacentHTML('beforeend', measureHTML);
    
    // 更新措施计数
    updateMeasureCount();
    
    // 更新进度
    updateProgress();
    
    // 为新添加的textarea初始化自动调整高度功能
    const newTextareas = container.querySelectorAll(`#measure-group-${newIndex} textarea`);
    newTextareas.forEach(textarea => {
        textarea.style.height = '38px';
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        textarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
    });
}

function removeMeasure(measureId) {
    const measureGroup = document.getElementById(`measure-group-${measureId}`);
    if (measureGroup) {
        measureGroup.remove();
        // 删除后重新排序所有措施
        reorderMeasures();
        updateMeasureCount();
        // 更新进度
        updateProgress();
    }
}

// 重新排序措施功能
function reorderMeasures() {
    const container = document.getElementById('dynamic-measures-container');
    const measures = Array.from(container.querySelectorAll('.measure-group'));
    
    // 按当前序号排序
    measures.sort((a, b) => {
        const aIndex = parseInt(a.id.split('-').pop());
        const bIndex = parseInt(b.id.split('-').pop());
        return aIndex - bIndex;
    });
    
    // 重新分配序号，从2开始（因为1是固定措施）
    measures.forEach((measureElement, index) => {
        const newIndex = index + 2;
        const oldId = measureElement.id;
        const oldIndex = parseInt(oldId.split('-').pop());
        
        if (newIndex !== oldIndex) {
            // 更新容器ID
            measureElement.id = `measure-group-${newIndex}`;
            
            // 更新组标题
            const groupTitle = measureElement.querySelector('.group-title');
            groupTitle.childNodes[0].textContent = `临时措施${newIndex}`;
            
            // 更新删除按钮的onclick
            const removeBtn = measureElement.querySelector('.remove-member-btn');
            removeBtn.setAttribute('onclick', `removeMeasure(${newIndex})`);
            
            // 更新所有字段的ID和name
            const fields = ['scope', 'measure', 'responsible', 'deadline', 'status', 'note'];
            fields.forEach(field => {
                const input = measureElement.querySelector(`[name="d3_${field}${oldIndex}"]`);
                const label = measureElement.querySelector(`label[for="d3_${field}${oldIndex}"]`);
                if (input && label) {
                    input.id = `d3_${field}${newIndex}`;
                    input.name = `d3_${field}${newIndex}`;
                    label.setAttribute('for', `d3_${field}${newIndex}`);
                }
            });
        }
    });
}

function updateMeasureCount() {
    const dynamicMeasures = document.querySelectorAll('#dynamic-measures-container .measure-group');
    const totalMeasures = 1 + dynamicMeasures.length; // 1个固定措施 + 动态措施
    document.getElementById('measure-count').textContent = totalMeasures;
}

// 原因分析功能
// 不再需要全局计数器，每次添加时动态查找可用序号

// D5措施相关函数
function addD5Measure() {
    const container = document.getElementById('dynamic-d5-measures-container');
    
    // 找到下一个可用的序号，从2开始（因为1是固定措施）
    let newIndex = 2;
    while (document.getElementById(`d5-measure-${newIndex}`)) {
        newIndex++;
    }
    
    // 创建新措施的HTML
    const measureHTML = `
        <div class="measure-group" id="d5-measure-${newIndex}">
            <div class="group-title">
                措施${newIndex}
                <button type="button" class="remove-member-btn" onclick="removeD5Measure(${newIndex})">删除</button>
            </div>
            <div class="form-row-d5">
                <div class="form-group">
                    <label for="d5_measure${newIndex}">纠正措施</label>
                    <textarea id="d5_measure${newIndex}" name="d5_measure${newIndex}" class="auto-resize-textarea"></textarea>
                </div>
                <div class="form-group">
                    <label for="d5_responsible${newIndex}">责任人</label>
                    <textarea id="d5_responsible${newIndex}" name="d5_responsible${newIndex}"></textarea>
                </div>
                <div class="form-group">
                                                        <label for="d5_deadline${newIndex}">计划完成日期</label>
                                    <input type="date" id="d5_deadline${newIndex}" name="d5_deadline${newIndex}">
                </div>
            </div>
        </div>
    `;
    
    // 添加到容器中
    container.insertAdjacentHTML('beforeend', measureHTML);
    
    // 更新措施计数
    updateD5MeasureCount();
    
    // 更新进度
    updateProgress();
    
    // 为新添加的textarea初始化自动调整高度功能
    const newTextareas = container.querySelectorAll(`#d5-measure-${newIndex} textarea`);
    newTextareas.forEach(textarea => {
        textarea.style.height = '38px';
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        textarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
    });
}

function removeD5Measure(measureId) {
    // 措施1不可删除
    if (measureId === 1) {
        return;
    }
    
    const measureGroup = document.getElementById(`d5-measure-${measureId}`);
    if (measureGroup) {
        measureGroup.remove();
        // 删除后重新排序所有措施
        reorderD5Measures();
        updateD5MeasureCount();
        // 更新进度
        updateProgress();
    }
}

// 重新排序D5措施功能
function reorderD5Measures() {
    // 获取固定措施1
    const fixedMeasure = document.getElementById('d5-measure-1');
    const container = document.getElementById('dynamic-d5-measures-container');
    const measures = Array.from(container.querySelectorAll('.measure-group'));
    
    // 按当前序号排序
    measures.sort((a, b) => {
        const aIndex = parseInt(a.id.split('-').pop());
        const bIndex = parseInt(b.id.split('-').pop());
        return aIndex - bIndex;
    });
    
    // 重新分配序号，从2开始（因为1是固定措施）
    measures.forEach((measureElement, index) => {
        const newIndex = index + 2;
        const oldId = measureElement.id;
        const oldIndex = parseInt(oldId.split('-').pop());
        
        if (newIndex !== oldIndex) {
            // 更新容器ID
            measureElement.id = `d5-measure-${newIndex}`;
            
            // 更新组标题
            const groupTitle = measureElement.querySelector('.group-title');
            groupTitle.childNodes[0].textContent = `措施${newIndex}`;
            
            // 更新删除按钮的onclick
            const removeBtn = measureElement.querySelector('.remove-member-btn');
            removeBtn.setAttribute('onclick', `removeD5Measure(${newIndex})`);
            
            // 更新所有字段的ID和name
            const fields = ['measure', 'responsible', 'deadline'];
            fields.forEach(field => {
                const input = measureElement.querySelector(`[name="d5_${field}${oldIndex}"]`);
                const label = measureElement.querySelector(`label[for="d5_${field}${oldIndex}"]`);
                if (input && label) {
                    input.id = `d5_${field}${newIndex}`;
                    input.name = `d5_${field}${newIndex}`;
                    label.setAttribute('for', `d5_${field}${newIndex}`);
                }
            });
        }
    });
}

function updateD5MeasureCount() {
    const fixedMeasure = document.getElementById('d5-measure-1') ? 1 : 0;
    const dynamicMeasures = document.querySelectorAll('#dynamic-d5-measures-container .measure-group');
    const totalMeasures = fixedMeasure + dynamicMeasures.length;
    document.getElementById('d5-measure-count').textContent = totalMeasures;
}

// D6验证相关函数
function addD6Verification() {
    const container = document.getElementById('dynamic-d6-verifications-container');
    
    // 找到下一个可用的序号，从2开始（因为1是固定验证）
    let newIndex = 2;
    while (document.getElementById(`d6-verification-${newIndex}`)) {
        newIndex++;
    }
    
    // 创建新验证的HTML
    const verificationHTML = `
        <div class="verification-group" id="d6-verification-${newIndex}">
            <div class="group-title">
                验证${newIndex}
                <button type="button" class="remove-member-btn" onclick="removeD6Verification(${newIndex})">删除</button>
            </div>
            <div class="form-row-d6">
                <div class="form-group">
                    <label for="d6_verification${newIndex}">措施验证</label>
                    <textarea id="d6_verification${newIndex}" name="d6_verification${newIndex}" class="auto-resize-textarea"></textarea>
                </div>
                <div class="form-group">
                    <label for="d6_verifier${newIndex}">验证人</label>
                    <textarea id="d6_verifier${newIndex}" name="d6_verifier${newIndex}"></textarea>
                </div>
                <div class="form-group">
                                                        <label for="d6_time${newIndex}">验证时间</label>
                                    <input type="date" id="d6_time${newIndex}" name="d6_time${newIndex}">
                </div>
                <div class="form-group">
                    <label for="d6_result${newIndex}">验证结果</label>
                    <textarea id="d6_result${newIndex}" name="d6_result${newIndex}" class="auto-resize-textarea"></textarea>
                </div>
            </div>
        </div>
    `;
    
    // 添加到容器中
    container.insertAdjacentHTML('beforeend', verificationHTML);
    
    // 更新验证计数
    updateD6VerificationCount();
    
    // 更新进度
    updateProgress();
    
    // 为新添加的textarea初始化自动调整高度功能
    const newTextareas = container.querySelectorAll(`#d6-verification-${newIndex} textarea`);
    newTextareas.forEach(textarea => {
        textarea.style.height = '38px';
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        textarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
    });
}

function removeD6Verification(verificationId) {
    // 验证1不可删除
    if (verificationId === 1) {
        return;
    }
    
    const verificationGroup = document.getElementById(`d6-verification-${verificationId}`);
    if (verificationGroup) {
        verificationGroup.remove();
        // 删除后重新排序所有验证
        reorderD6Verifications();
        updateD6VerificationCount();
        // 更新进度
        updateProgress();
    }
}

// 重新排序D6验证功能
function reorderD6Verifications() {
    // 获取固定验证1
    const fixedVerification = document.getElementById('d6-verification-1');
    const container = document.getElementById('dynamic-d6-verifications-container');
    const verifications = Array.from(container.querySelectorAll('.verification-group'));
    
    // 按当前序号排序
    verifications.sort((a, b) => {
        const aIndex = parseInt(a.id.split('-').pop());
        const bIndex = parseInt(b.id.split('-').pop());
        return aIndex - bIndex;
    });
    
    // 重新分配序号，从2开始（因为1是固定验证）
    verifications.forEach((verificationElement, index) => {
        const newIndex = index + 2;
        const oldId = verificationElement.id;
        const oldIndex = parseInt(oldId.split('-').pop());
        
        if (newIndex !== oldIndex) {
            // 更新容器ID
            verificationElement.id = `d6-verification-${newIndex}`;
            
            // 更新组标题
            const groupTitle = verificationElement.querySelector('.group-title');
            groupTitle.childNodes[0].textContent = `验证${newIndex}`;
            
            // 更新删除按钮的onclick
            const removeBtn = verificationElement.querySelector('.remove-member-btn');
            removeBtn.setAttribute('onclick', `removeD6Verification(${newIndex})`);
            
            // 更新所有字段的ID和name
            const fields = ['verification', 'verifier', 'time', 'result'];
            fields.forEach(field => {
                const input = verificationElement.querySelector(`[name="d6_${field}${oldIndex}"]`);
                const label = verificationElement.querySelector(`label[for="d6_${field}${oldIndex}"]`);
                if (input && label) {
                    input.id = `d6_${field}${newIndex}`;
                    input.name = `d6_${field}${newIndex}`;
                    label.setAttribute('for', `d6_${field}${newIndex}`);
                }
            });
        }
    });
}

function updateD6VerificationCount() {
    const fixedVerification = document.getElementById('d6-verification-1') ? 1 : 0;
    const dynamicVerifications = document.querySelectorAll('#dynamic-d6-verifications-container .verification-group');
    const totalVerifications = fixedVerification + dynamicVerifications.length;
    document.getElementById('d6-verification-count').textContent = totalVerifications;
}

// D7预防措施相关函数
function addD7Prevention() {
    const container = document.getElementById('dynamic-d7-preventions-container');
    
    // 找到下一个可用的序号，从2开始（因为1是固定预防措施）
    let newIndex = 2;
    while (document.getElementById(`d7-prevention-${newIndex}`)) {
        newIndex++;
    }
    
    // 创建新预防措施的HTML
    const preventionHTML = `
        <div class="prevention-group" id="d7-prevention-${newIndex}">
            <div class="group-title">
                预防${newIndex}
                <button type="button" class="remove-member-btn" onclick="removeD7Prevention(${newIndex})">删除</button>
            </div>
            <div class="form-row-d7">
                <div class="form-group">
                    <label for="d7_prevention${newIndex}">预防措施</label>
                    <textarea id="d7_prevention${newIndex}" name="d7_prevention${newIndex}" class="auto-resize-textarea"></textarea>
                </div>
                <div class="form-group">
                    <label for="d7_responsible${newIndex}">责任人</label>
                    <textarea id="d7_responsible${newIndex}" name="d7_responsible${newIndex}"></textarea>
                </div>
                <div class="form-group">
                                                        <label for="d7_deadline${newIndex}">计划完成日期</label>
                                    <input type="date" id="d7_deadline${newIndex}" name="d7_deadline${newIndex}">
                </div>
            </div>
        </div>
    `;
    
    // 添加到容器中
    container.insertAdjacentHTML('beforeend', preventionHTML);
    
    // 更新预防措施计数
    updateD7PreventionCount();
    
    // 更新进度
    updateProgress();
    
    // 为新添加的textarea初始化自动调整高度功能
    const newTextareas = container.querySelectorAll(`#d7-prevention-${newIndex} textarea`);
    newTextareas.forEach(textarea => {
        textarea.style.height = '38px';
        textarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        textarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
    });
}

function removeD7Prevention(preventionId) {
    // 预防1不可删除
    if (preventionId === 1) {
        return;
    }
    
    const preventionGroup = document.getElementById(`d7-prevention-${preventionId}`);
    if (preventionGroup) {
        preventionGroup.remove();
        // 删除后重新排序所有预防措施
        reorderD7Preventions();
        updateD7PreventionCount();
        // 更新进度
        updateProgress();
    }
}

// 重新排序D7预防措施功能
function reorderD7Preventions() {
    // 获取固定预防1
    const fixedPrevention = document.getElementById('d7-prevention-1');
    const container = document.getElementById('dynamic-d7-preventions-container');
    const preventions = Array.from(container.querySelectorAll('.prevention-group'));
    
    // 按当前序号排序
    preventions.sort((a, b) => {
        const aIndex = parseInt(a.id.split('-').pop());
        const bIndex = parseInt(b.id.split('-').pop());
        return aIndex - bIndex;
    });
    
    // 重新分配序号，从2开始（因为1是固定预防措施）
    preventions.forEach((preventionElement, index) => {
        const newIndex = index + 2;
        const oldId = preventionElement.id;
        const oldIndex = parseInt(oldId.split('-').pop());
        
        if (newIndex !== oldIndex) {
            // 更新容器ID
            preventionElement.id = `d7-prevention-${newIndex}`;
            
            // 更新组标题
            const groupTitle = preventionElement.querySelector('.group-title');
            groupTitle.childNodes[0].textContent = `预防${newIndex}`;
            
            // 更新删除按钮的onclick
            const removeBtn = preventionElement.querySelector('.remove-member-btn');
            removeBtn.setAttribute('onclick', `removeD7Prevention(${newIndex})`);
            
            // 更新所有字段的ID和name
            const fields = ['prevention', 'responsible', 'deadline'];
            fields.forEach(field => {
                const input = preventionElement.querySelector(`[name="d7_${field}${oldIndex}"]`);
                const label = preventionElement.querySelector(`label[for="d7_${field}${oldIndex}"]`);
                if (input && label) {
                    input.id = `d7_${field}${newIndex}`;
                    input.name = `d7_${field}${newIndex}`;
                    label.setAttribute('for', `d7_${field}${newIndex}`);
                }
            });
        }
    });
}

function updateD7PreventionCount() {
    const fixedPrevention = document.getElementById('d7-prevention-1') ? 1 : 0;
    const dynamicPreventions = document.querySelectorAll('#dynamic-d7-preventions-container .prevention-group');
    const totalPreventions = fixedPrevention + dynamicPreventions.length;
    document.getElementById('d7-prevention-count').textContent = totalPreventions;
}

// 添加原因功能
function addCause(category, label) {
    const container = document.getElementById(`${category}-causes-container`);
    
    // 找到下一个可用的序号
    let newIndex = 1;
    while (document.getElementById(`${category}-cause-${newIndex}`)) {
        newIndex++;
    }
    
    // 创建新原因的HTML
    const causeHTML = `
        <div class="form-row-d4-analysis" id="${category}-cause-${newIndex}">
            <div class="form-group">
                <label for="d4_${category}${newIndex}">${label}${newIndex}</label>
                <textarea id="d4_${category}${newIndex}" name="d4_${category}${newIndex}" class="auto-resize-textarea"></textarea>
            </div>
            <div class="form-group">
                <label for="d4_${category}${newIndex}_judgment">判定</label>
                <select id="d4_${category}${newIndex}_judgment" name="d4_${category}${newIndex}_judgment">
                    <option value="">请选择</option>
                    <option value="主因">主因</option>
                    <option value="次因">次因</option>
                    <option value="排除">排除</option>
                </select>
            </div>
            <div class="form-group">
                <label for="d4_${category}${newIndex}_evidence">证据</label>
                <textarea id="d4_${category}${newIndex}_evidence" name="d4_${category}${newIndex}_evidence" class="auto-resize-textarea"></textarea>
            </div>
            <div class="form-group inline-actions">
                <button type="button" class="remove-cause-btn" onclick="removeCause('${category}', ${newIndex})">删除</button>
            </div>
        </div>
    `;
    
    // 添加到容器中
    container.insertAdjacentHTML('beforeend', causeHTML);
    
    // 为新添加的textarea初始化自动调整高度功能
    const newTextarea = document.getElementById(`d4_${category}${newIndex}`);
    if (newTextarea) {
        newTextarea.style.height = '38px';
        newTextarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
        newTextarea.addEventListener('paste', function() {
            setTimeout(() => {
                autoResizeTextarea(this);
            }, 5);
        });
        newTextarea.addEventListener('keyup', function() {
            autoResizeTextarea(this);
        });
        newTextarea.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => {
                    autoResizeTextarea(this);
                }, 1);
            }
        });
    }
    
    // 更新进度
    updateProgress();
}

// 删除原因功能
function removeCause(category, causeId) {
    // 禁止删除原因1，确保每个类别至少保留一个原因
    if (causeId === 1) {
        alert('每个分析类别至少需要保留一个原因，原因1不可删除。');
        return;
    }
    
    const causeElement = document.getElementById(`${category}-cause-${causeId}`);
    if (causeElement) {
        causeElement.remove();
        // 删除后重新排序所有原因
        reorderCauses(category);
        // 更新进度
        updateProgress();
    }
}

// 重新排序原因功能
function reorderCauses(category) {
    const container = document.getElementById(`${category}-causes-container`);
    const causes = Array.from(container.querySelectorAll('.form-row-d4-analysis'));
    
    // 过滤出需要重新排序的原因（排除固定的原因1）
    const dynamicCauses = causes.filter(causeElement => {
        const currentIndex = parseInt(causeElement.id.split('-').pop());
        return currentIndex > 1; // 只重新排序原因2及以上
    });
    
    // 按当前序号排序
    dynamicCauses.sort((a, b) => {
        const aIndex = parseInt(a.id.split('-').pop());
        const bIndex = parseInt(b.id.split('-').pop());
        return aIndex - bIndex;
    });
    
    // 重新分配序号，从2开始（因为1是固定原因）
    dynamicCauses.forEach((causeElement, index) => {
        const newIndex = index + 2; // 从2开始编号
        const oldId = causeElement.id;
        const oldIndex = parseInt(oldId.split('-').pop());
        
        if (newIndex !== oldIndex) {
            // 更新容器ID
            causeElement.id = `${category}-cause-${newIndex}`;
            
            // 更新标签文本（第一个label是原因标签）
            const firstLabel = causeElement.querySelector('label');
            if (firstLabel) {
                const labelText = firstLabel.textContent;
                const labelPrefix = labelText.replace(/\d+$/, ''); // 移除末尾数字
                firstLabel.textContent = labelPrefix + newIndex;
            }
            
            // 更新删除按钮的onclick
            const removeBtn = causeElement.querySelector('.remove-cause-btn');
            if (removeBtn) {
                removeBtn.setAttribute('onclick', `removeCause('${category}', ${newIndex})`);
            }
            
            // 更新所有字段的ID和name
            const fields = ['', '_judgment', '_evidence']; // 原因描述、判定、证据
            fields.forEach(suffix => {
                const fieldName = `d4_${category}${oldIndex}${suffix}`;
                const newFieldName = `d4_${category}${newIndex}${suffix}`;
                
                const input = causeElement.querySelector(`[name="${fieldName}"]`);
                const label = causeElement.querySelector(`label[for="${fieldName}"]`);
                
                if (input && label) {
                    input.id = newFieldName;
                    input.name = newFieldName;
                    label.setAttribute('for', newFieldName);
                }
            });
        }
    });
}

// 表单验证功能
function validateRequiredFields() {
    const requiredFields = [
        // D0 必填字段
        { id: 'd0_background', name: '项目背景' },
        
        // D2 必填字段  
        { id: 'd2_description', name: '事件整体描述' },
        { id: 'd2_when', name: '何时发生' },
        { id: 'd2_where', name: '何地发生' },
        { id: 'd2_who', name: '何人发现' },
        { id: 'd2_what', name: '发生了什么问题' },
        { id: 'd2_why', name: '为什么发生这问题' },
        { id: 'd2_how', name: '问题如何发生' },
        { id: 'd2_impact', name: '问题数量和影响程度' }
    ];

    const missingFields = [];
    
    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (element && element.value.trim() === '') {
            missingFields.push(field.name);
            element.style.border = '2px solid #dc3545';
        } else if (element) {
            element.style.border = '';
        }
    });

    return missingFields;
}

// 清除验证错误样式
function clearValidationErrors() {
    const requiredFields = document.querySelectorAll('input[required], textarea[required]');
    requiredFields.forEach(field => {
        field.style.border = '';
    });
}

// 多记录管理系统
const RECORDS_KEY = '8d_form_records';
let currentRecordId = null;

// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 获取所有记录
function getAllRecords() {
    try {
        const records = localStorage.getItem(RECORDS_KEY);
        return records ? JSON.parse(records) : {};
    } catch (error) {
        console.error('获取记录失败:', error);
        return {};
    }
}

// 保存记录到localStorage
function saveRecords(records) {
    try {
        localStorage.setItem(RECORDS_KEY, JSON.stringify(records));
    } catch (error) {
        console.error('保存记录失败:', error);
    }
}

// 生成记录标题
function generateRecordTitle(formData) {
    const title = formData.d0_title || '';
    const background = formData.d0_background || '';
    const description = formData.d2_description || '';
    
    if (title.trim()) {
        return title.trim();
    } else if (background.trim()) {
        return background.length > 30 ? background.substring(0, 30) + '...' : background;
    } else if (description.trim()) {
        return description.length > 30 ? description.substring(0, 30) + '...' : description;
    } else {
        return '未命名记录';
    }
}

// 收集当前表单数据
function collectFormData() {
    const form = document.querySelector('#d8-form') || document.querySelector('form');
    if (!form) {
        console.error('无法找到表单元素');
        return {};
    }
    
    const data = {};
    let totalFields = 0;
    let fieldsWithValues = 0;
    
    // 收集所有input、textarea、select字段，包括空值
    const fields = form.querySelectorAll('input, textarea, select');
    fields.forEach(field => {
        totalFields++;
        
        if (field.name && field.type !== 'button' && field.type !== 'submit') {
            // 保存所有字段的值，包括空值
            data[field.name] = field.value || '';
            if (field.value && field.value.trim() !== '') {
                fieldsWithValues++;
                console.log(`📝 收集字段 ${field.name}: "${field.value}"`);
            }
        }
        // 对于没有name但有id的字段也收集
        if (!field.name && field.id && field.type !== 'button' && field.type !== 'submit') {
            data[field.id] = field.value || '';
            if (field.value && field.value.trim() !== '') {
                fieldsWithValues++;
                console.log(`📝 收集字段 ${field.id}: "${field.value}"`);
            }
        }
    });
    
    console.log(`表单扫描完成: 找到 ${totalFields} 个字段，其中 ${fieldsWithValues} 个有值`);
    console.log('Form found:', form.id || 'no-id');
    console.log('Collected data count:', Object.keys(data).length);
    
    // 特别检查关键字段
    const keyFields = ['d0_background', 'd2_description', 'd2_when', 'd2_where'];
    keyFields.forEach(fieldName => {
        const element = document.getElementById(fieldName);
        if (element) {
            console.log(`🔍 关键字段 ${fieldName}: 元素存在=${!!element}, 值="${element.value}", 在数据中="${data[fieldName]}"`);
        } else {
            console.warn(`⚠️ 关键字段 ${fieldName}: 元素不存在`);
        }
    });
    
    return data;
}

// 检查表单是否为空
function isFormEmpty(data) {
    return !Object.values(data).some(value => value && value.trim() !== '');
}

// 新建表单
function newRecord() {
    if (confirm('确认新建表单？当前填写的内容如果未保存将会丢失。')) {
        clearForm();
        currentRecordId = null;
        updateRecordsList();
    }
}



// 提交成功后自动保存为已提交记录
function saveSubmittedRecord() {
    const formData = collectFormData();
    
    if (isFormEmpty(formData)) {
        return;
    }
    
    const records = getAllRecords();
    const recordId = generateId(); // 总是生成新ID，避免覆盖草稿
    const recordTitle = generateRecordTitle(formData);
    
    records[recordId] = {
        id: recordId,
        title: recordTitle,
        data: formData,
        type: 'submitted', // 标记为已提交
        timestamp: new Date().toISOString(),
        lastModified: new Date().toISOString()
    };
    
    saveRecords(records);
    console.log('已提交的报告已自动保存为记录');
    
    // 如果当前是草稿状态，询问是否删除草稿
    if (currentRecordId && records[currentRecordId] && records[currentRecordId].type === 'draft') {
        if (confirm('提交成功！是否删除对应的草稿记录？')) {
            delete records[currentRecordId];
            saveRecords(records);
        }
    }
    currentRecordId = null;
}

// 加载记录
function loadRecord(recordId) {
    const records = getAllRecords();
    const record = records[recordId];
    
    if (!record) {
        alert('记录不存在');
        return;
    }
    
    if (confirm(`确认加载记录"${record.title}"？当前填写的内容将会被覆盖。`)) {
        clearForm();
        
        // 恢复表单数据
        Object.keys(record.data).forEach(key => {
            const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
            if (element && record.data[key]) {
                element.value = record.data[key];
                // 触发textarea自动调整高度
                if (element.tagName === 'TEXTAREA') {
                    autoResizeTextarea(element);
                }
            }
        });
        
        currentRecordId = recordId;
        
        // 更新计数器
        updateMemberCount();
        updateMeasureCount();
        updateD5MeasureCount();
        updateD6VerificationCount();
        updateD7PreventionCount();
        
        // 更新进度
        updateProgress();
        
        alert('记录加载成功！');
    }
}

// 删除记录
function deleteRecord(recordId) {
    const records = getAllRecords();
    const record = records[recordId];
    
    if (!record) {
        alert('记录不存在');
        return;
    }
    
    if (confirm(`确认删除记录"${record.title}"？此操作不可撤销。`)) {
        delete records[recordId];
        saveRecords(records);
        
        if (currentRecordId === recordId) {
            currentRecordId = null;
        }
        
        alert('记录删除成功！');
        updateRecordsList();
    }
}

// 清空表单
function clearForm() {
    try {
        const form = document.querySelector('form');
        if (!form) {
            console.warn('clearForm: 找不到表单元素');
            return;
        }

        form.reset();

        // 重置textarea高度
        try {
            const textareas = form.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                try {
                    textarea.style.height = '38px';
                } catch (textareaError) {
                    console.warn('重置textarea高度失败:', textarea, textareaError);
                }
            });
        } catch (textareaError) {
            console.warn('处理textarea时出错:', textareaError);
        }

        // 清除验证错误
        try {
            clearValidationErrors();
        } catch (validationError) {
            console.warn('清除验证错误失败:', validationError);
        }

        // 重置动态内容到初始状态
        try {
            // 清除动态添加的成员
            const dynamicMembersContainer = document.getElementById('dynamic-members-container');
            if (dynamicMembersContainer) {
                dynamicMembersContainer.innerHTML = '';
            }

            // 清除动态添加的措施
            const containers = [
                'dynamic-measures-container',
                'dynamic-d5-measures-container',
                'dynamic-d6-verifications-container',
                'dynamic-d7-preventions-container'
            ];

            containers.forEach(containerId => {
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = '';
                }
            });
        } catch (dynamicError) {
            console.warn('清除动态内容失败:', dynamicError);
        }

        // 重置计数器
        try {
            updateMemberCount();
            updateMeasureCount();
            updateD5MeasureCount();
            updateD6VerificationCount();
            updateD7PreventionCount();
        } catch (counterError) {
            console.warn('重置计数器失败:', counterError);
        }

        // 更新进度
        try {
            updateProgress();
        } catch (progressError) {
            console.warn('更新进度失败:', progressError);
        }

    } catch (error) {
        console.error('clearForm函数执行失败:', error);
        throw error; // 重新抛出错误，让调用者知道清空失败
    }
}

// 更新记录列表显示
function updateRecordsList() {
    const records = getAllRecords();
    const recordsList = document.getElementById('records-list');
    const noRecords = document.getElementById('no-records');
    
    // 清除现有记录项
    const existingItems = recordsList.querySelectorAll('.record-item');
    existingItems.forEach(item => item.remove());
    
    const recordsArray = Object.values(records).sort((a, b) => 
        new Date(b.lastModified) - new Date(a.lastModified)
    );
    
    if (recordsArray.length === 0) {
        noRecords.style.display = 'block';
    } else {
        noRecords.style.display = 'none';
        
        recordsArray.forEach(record => {
            const recordItem = document.createElement('div');
            recordItem.className = 'record-item';
            
            // 确定记录类型和状态标识
            const recordType = record.type || 'submitted'; // 默认为已提交（兼容旧数据）
            const typeLabel = recordType === 'draft' ? '草稿' : '已提交';
            const typeClass = recordType === 'draft' ? 'type-draft' : 'type-submitted';
            
            recordItem.innerHTML = `
                <div class="record-info">
                    <div class="record-title">
                        ${record.title}
                        <span class="record-type ${typeClass}">${typeLabel}</span>
                    </div>
                    <div class="record-meta">
                        ${recordType === 'draft' ? '保存时间' : '提交时间'}：${new Date(record.timestamp).toLocaleString('zh-CN')}
                        ${record.lastModified !== record.timestamp ? 
                          `| 最后修改：${new Date(record.lastModified).toLocaleString('zh-CN')}` : ''}
                    </div>
                </div>
                <div class="record-actions">
                    <button class="record-action-btn load-btn" onclick="loadRecord('${record.id}')">
                        ${recordType === 'draft' ? '继续编辑' : '查看/复用'}
                    </button>
                    <button class="record-action-btn delete-btn" onclick="deleteRecord('${record.id}')">
                        删除
                    </button>
                </div>
            `;
            
            recordsList.appendChild(recordItem);
        });
    }
}

// 自动保存（防抖处理，只对草稿进行自动保存）
let saveTimer = null;
function autoSave() {
    if (saveTimer) {
        clearTimeout(saveTimer);
    }
    saveTimer = setTimeout(() => {
        if (currentRecordId) {
            const records = getAllRecords();
            const currentRecord = records[currentRecordId];
            
            // 只对草稿类型的记录进行自动保存
            if (currentRecord && currentRecord.type === 'draft') {
                const formData = collectFormData();
                if (!isFormEmpty(formData)) {
                    currentRecord.data = formData;
                    currentRecord.lastModified = new Date().toISOString();
                    saveRecords(records);
                    console.log('草稿已自动保存');
                }
            }
        }
    }, 3000); // 3秒后保存
}

// 提交确认和防重复提交
let isSubmitting = false;

function handleFormSubmit(event) {
    event.preventDefault();
    
    // 防重复提交
    if (isSubmitting) {
        alert('正在提交中，请勿重复提交...');
        return false;
    }
    
    // 清除之前的验证错误
    clearValidationErrors();
    
    // 验证必填字段
    const missingFields = validateRequiredFields();
    if (missingFields.length > 0) {
        alert(`请填写以下必填字段：\n${missingFields.join('\n')}`);
        return false;
    }
    
    // 开始AI生成流程
    isSubmitting = true;
    const submitBtn = document.querySelector('.submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    btnText.style.display = 'none';
    btnLoading.style.display = 'flex';
    submitBtn.disabled = true;
    
    // 收集表单数据
    const form = event.target;
    const formData = collectFormData(); // 使用现有的数据收集函数
    
    console.log('Collected form data:', formData);
    console.log('Sending to:', form.action);
    
    // 测试用：显示收集到的数据
    if (Object.keys(formData).length === 0) {
        alert('警告：未收集到表单数据');
        return false;
    }
    
    // 将表单数据转换为D0-D8结构
    const templateData = convertFormDataToTemplateFormat(formData);
    const d0ToD8Data = templateData.replacements; // 只提取replacements部分
    
    console.log('D0-D8 data to send:', d0ToD8Data);
    
    // 发送AJAX请求到后端，只发送D0-D8的键值对
    fetch(form.action, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(d0ToD8Data)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        if (response.ok) {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                // 如果不是JSON响应，读取文本内容
                return response.text().then(text => {
                    console.log('Received non-JSON response:', text.substring(0, 200) + '...');
                    throw new Error('服务器返回了HTML页面而不是JSON响应');
                });
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    })
    .then(data => {
        if (data.status === 'success') {
            // 设置会话ID
            window.currentSessionId = data.session_id;
            
            // 创建或更新对话
            let conversationId = window.conversationUI ? window.conversationUI.currentConversationId : null;
            
            if (!conversationId) {
                // 创建新对话，使用原始表单数据
                conversationId = window.conversationManager.createConversation(formData);
                if (window.conversationUI) {
                    window.conversationUI.currentConversationId = conversationId;
                }
            }
            
            // 如果有AI增强数据，创建新版本
            if (data.enhanced_data) {
                const conversation = window.conversationManager.conversations.get(conversationId);
                if (conversation) {
                    // 添加AI增强版本
                    window.conversationManager.addFormVersion(conversation, data.enhanced_data, 'ai');
                    
                    // 添加系统消息
                    window.conversationManager.addChatMessage('ai', 
                        '我已经基于您提供的信息完善了8D报告的内容，并将优化后的数据回填到了左侧表单中。您可以查看各个章节的内容，如有需要可以继续与我对话进行调整。'
                    );
                    
                    // 显示数据回填成功提示
                    showAIStatus('success', '✅ AI已完善您的报告并创建新版本');
                }
            } else {
                // 没有AI增强数据，只添加欢迎消息
                const conversation = window.conversationManager.conversations.get(conversationId);
                if (conversation) {
                    window.conversationManager.addChatMessage('ai', 
                        '您好！我是您的8D报告助手。我已经收到了您的表单数据，您可以向我提问或要求我帮助完善报告内容。'
                    );
                }
                
                showAIStatus('success', '✅ 表单已提交，AI面板已开启');
            }
            
            // 显示AI面板
            showAIPanel();
            
            // 调试功能：在命令行打印JSON数据
            console.log('提交的D0-D8数据:', formData);
            console.log('AI增强数据:', data.enhanced_data);
            
        } else {
            throw new Error(data.error || '处理失败');
        }
    })
    .catch(error => {
        console.error('提交错误:', error);
        alert('提交失败，请重试：' + error.message);
    })
    .finally(() => {
        // 恢复提交按钮
        isSubmitting = false;
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        submitBtn.disabled = false;
    });
    
    return false;
}

// 进度指示器和章节导航功能
let currentActiveSection = 'guide';
let isNavigating = false;
let scrollTimer = null;

// 章节字段配置
const sectionFields = {
    'guide': [], // 使用说明章节
    'd0': ['d0_title', 'd0_reporter', 'd0_time', 'd0_background'], // D0有4个字段，其中d0_background是必填
    'd1': [], // 非必填，根据动态内容计算
    'd2': ['d2_description', 'd2_when', 'd2_where', 'd2_who', 'd2_what', 'd2_why', 'd2_how', 'd2_impact'], // 必填字段
    'd3': [], // 非必填，根据动态内容计算
    'd4': [], // 非必填，根据动态内容计算
    'd5': [], // 非必填，根据动态内容计算
    'd6': [], // 非必填，根据动态内容计算
    'd7': [], // 非必填，根据动态内容计算
    'd8': [] // 非必填，根据动态内容计算
};

// 切换章节折叠状态
function toggleSection(sectionId) {
    const section = document.getElementById(`section-${sectionId}`);
    const collapseBtn = document.getElementById(`collapse-btn-${sectionId}`);
    
    if (section.classList.contains('collapsed')) {
        section.classList.remove('collapsed');
        collapseBtn.querySelector('.collapse-icon').textContent = '−';
    } else {
        section.classList.add('collapsed');
        collapseBtn.querySelector('.collapse-icon').textContent = '+';
    }
}

// 计算章节完成度
function calculateSectionCompletion(sectionId) {
    try {
        const section = document.getElementById(`section-${sectionId}`);
        if (!section) return { completed: 0, total: 0, isComplete: false };

        // 获取章节内所有输入字段
        const allFields = section.querySelectorAll('input, textarea, select');
        const filledFields = Array.from(allFields).filter(field => field.value.trim() !== '');

        // 对于有预定义字段的章节（如D0, D2），使用预定义字段数
        const predefinedFields = sectionFields[sectionId] || [];
        const totalCount = predefinedFields.length > 0 ? predefinedFields.length : Math.max(allFields.length, 1);

        // 检查必填字段完成情况（仅对有必填字段的章节）
        let isComplete;
        if (sectionId === 'd0') {
            // D0只有background是必填
            const backgroundField = document.getElementById('d0_background');
            isComplete = backgroundField && backgroundField.value.trim() !== '';
        } else if (sectionId === 'd2') {
            // D2所有字段都是必填
            isComplete = predefinedFields.length > 0 && predefinedFields.every(fieldId => {
                const field = document.getElementById(fieldId);
                return field && field.value.trim() !== '';
            });
        } else {
            // 其他章节有内容就算完成
            isComplete = filledFields.length > 0;
        }

        return {
            completed: filledFields.length,
            total: totalCount,
            isComplete: isComplete
        };
    } catch (error) {
        console.warn(`calculateSectionCompletion(${sectionId})失败:`, error);
        return { completed: 0, total: 0, isComplete: false };
    }
}

// 更新单个章节的完成状态
function updateSectionStatus(sectionId) {
    try {
        const completion = calculateSectionCompletion(sectionId);
        const badge = document.getElementById(`completion-${sectionId}`);
        const navItem = document.querySelector(`[data-section="${sectionId}"]`);
        const step = document.querySelector(`.step[data-section="${sectionId}"]`);
        const navProgressMini = document.getElementById(`nav-progress-${sectionId}`);

        // 使用说明章节特殊处理
        if (sectionId === 'guide') {
            if (badge) badge.textContent = '说明';
            if (navProgressMini) navProgressMini.textContent = '-';
            return;
        }

        // 更新完成度标识
        if (badge) badge.textContent = `${completion.completed}/${completion.total}`;

        // 更新导航栏中的小进度
        if (navProgressMini) {
            navProgressMini.textContent = `${completion.completed}/${completion.total}`;
        }

        // 更新样式
        if (completion.isComplete) {
            if (badge) {
                badge.classList.add('completed');
                badge.classList.remove('partial');
            }
            if (navItem) navItem.classList.add('completed');
            if (step) {
                step.classList.add('completed');
                step.classList.remove('active');
            }
        } else if (completion.completed > 0) {
            if (badge) {
                badge.classList.add('partial');
                badge.classList.remove('completed');
            }
            if (navItem) navItem.classList.remove('completed');
            if (step) {
                step.classList.remove('completed', 'active');
            }
        } else {
            if (badge) {
                badge.classList.remove('completed', 'partial');
            }
            if (navItem) navItem.classList.remove('completed');
            if (step) {
                step.classList.remove('completed', 'active');
            }
        }
    } catch (error) {
        console.warn(`updateSectionStatus(${sectionId})失败:`, error);
    }
}

// 更新整体进度
function updateProgress() {
    try {
        const allSections = ['guide', 'd0', 'd1', 'd2', 'd3', 'd4', 'd5', 'd6', 'd7', 'd8'];

        allSections.forEach(sectionId => {
            try {
                updateSectionStatus(sectionId);
            } catch (sectionError) {
                console.warn(`更新章节${sectionId}状态失败:`, sectionError);
            }
        });
    } catch (error) {
        console.warn('updateProgress失败:', error);
    }
}

// 获取导航栏高度
function getNavigationHeight() {
    const fixedNav = document.querySelector('.fixed-navigation');
    return fixedNav ? fixedNav.offsetHeight : 80;
}

// 章节导航功能
function navigateToSection(sectionId) {
    const targetSection = document.getElementById(`section-${sectionId}`);
    if (!targetSection) {
        return;
    }
    
    // 设置导航状态，防止滚动监听干扰
    isNavigating = true;
    
    // 展开目标章节
    targetSection.classList.remove('collapsed');
    const collapseBtn = document.getElementById(`collapse-btn-${sectionId}`);
    if (collapseBtn) {
        collapseBtn.querySelector('.collapse-icon').textContent = '−';
    }
    
    // 立即更新高亮状态
    setActiveSection(sectionId);
    
    // 计算滚动位置
    const navHeight = getNavigationHeight();
    const sectionTop = targetSection.offsetTop;
    const scrollPosition = Math.max(0, sectionTop - navHeight - 20);
    
    // 平滑滚动到目标位置
    window.scrollTo({
        top: scrollPosition,
        behavior: 'smooth'
    });
    
    // 1.5秒后重新启用滚动监听
    setTimeout(function() {
        isNavigating = false;
    }, 1500);
}

// 设置活动章节
function setActiveSection(sectionId) {
    if (currentActiveSection === sectionId) return;
    
    currentActiveSection = sectionId;
    
    // 清除所有导航项的活动状态
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelectorAll('.step').forEach(step => {
        if (!step.classList.contains('completed')) {
            step.classList.remove('active');
        }
    });
    
    // 清除右侧导航栏的活动状态
    document.querySelectorAll('.nav-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 设置当前活动章节的高亮
    const activeNavItem = document.querySelector(`.nav-item[data-section="${sectionId}"]`);
    const activeStep = document.querySelector(`.step[data-section="${sectionId}"]`);
    const activeNavSection = document.querySelector(`.nav-section[data-section="${sectionId}"]`);
    
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
    
    if (activeStep && !activeStep.classList.contains('completed')) {
        activeStep.classList.add('active');
    }
    
    if (activeNavSection) {
        activeNavSection.classList.add('active');
    }
}

// 获取当前视口中最显著的章节 - 重写版本
function getCurrentVisibleSection() {
    const sections = document.querySelectorAll('.form-section[data-section]');
    const navHeight = getNavigationHeight();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    // 视口中心位置
    const viewportCenter = scrollTop + navHeight + (window.innerHeight - navHeight) / 2;
    
    let bestSection = 'guide';
    let minDistance = Infinity;
    
    sections.forEach(section => {
        const sectionId = section.dataset.section;
        const sectionTop = section.offsetTop;
        const sectionBottom = sectionTop + section.offsetHeight;
        
        // 如果章节在视口范围内
        if (sectionTop <= scrollTop + window.innerHeight && sectionBottom >= scrollTop + navHeight) {
            // 计算章节中心与视口中心的距离
            const sectionCenter = (sectionTop + sectionBottom) / 2;
            const distance = Math.abs(sectionCenter - viewportCenter);
            
            if (distance < minDistance) {
                minDistance = distance;
                bestSection = sectionId;
            }
        }
        // 如果章节顶部刚好进入视口上方
        else if (sectionTop <= scrollTop + navHeight + 100 && sectionBottom > scrollTop + navHeight) {
            const distance = Math.abs(sectionTop - (scrollTop + navHeight));
            if (distance < minDistance) {
                minDistance = distance;
                bestSection = sectionId;
            }
        }
    });
    
    return bestSection;
}

// 滚动监听
function initScrollTracking() {
    // 添加滚动监听器
    window.addEventListener('scroll', function() {
        // 如果正在导航中，跳过滚动监听
        if (isNavigating) return;
        
        // 清除之前的定时器
        if (scrollTimer) {
            clearTimeout(scrollTimer);
        }
        
        // 延迟执行，避免频繁更新
        scrollTimer = setTimeout(function() {
            const visibleSection = getCurrentVisibleSection();
            
            if (visibleSection && visibleSection !== currentActiveSection) {
                setActiveSection(visibleSection);
            }
        }, 100);
    }, { passive: true });
}

// 绑定导航点击事件
function initNavigation() {
    // 章节导航点击
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const sectionId = this.dataset.section;
            navigateToSection(sectionId);
        });
    });
    
    // 进度步骤点击
    document.querySelectorAll('.step').forEach(step => {
        step.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const sectionId = this.dataset.section;
            navigateToSection(sectionId);
        });
    });
    
    // 右侧导航栏点击
    document.querySelectorAll('.nav-section').forEach(section => {
        section.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const sectionId = this.dataset.section;
            navigateToSection(sectionId);
        });
    });
}

// 显示记录面板
function showRecordsPanel() {
    const panel = document.getElementById('records-panel');
    if (panel) {
        panel.style.display = 'block';
        updateRecordsList(); // 刷新记录列表
    }
}

// 关闭记录面板
function closeRecordsPanel() {
    const panel = document.getElementById('records-panel');
    if (panel) {
        panel.style.display = 'none';
    }
}

// ==================== AI协作相关功能 ====================

// AI面板相关变量
let currentConversationId = null;
let isAIResponding = false;

// 显示AI面板
function showAIPanel() {
    const aiPanel = document.getElementById('ai-panel');
    const floatBtn = document.getElementById('ai-float-btn');
    
    if (aiPanel) {
        // 先显示面板，然后添加动画类
        aiPanel.style.display = 'flex';
        
        // 隐藏浮动按钮
        if (floatBtn) {
            floatBtn.style.display = 'none';
        }
        
        // 使用requestAnimationFrame确保动画能正常播放
        requestAnimationFrame(() => {
            aiPanel.classList.add('show');
        });
        
        // 初始化AI面板事件
        initAIPanelEvents();
    }
}

// 隐藏AI面板
function hideAIPanel() {
    const aiPanel = document.getElementById('ai-panel');
    const floatBtn = document.getElementById('ai-float-btn');
    
    if (aiPanel) {
        // 先移除动画类，然后隐藏面板
        aiPanel.classList.remove('show');
        
        // 等待动画完成后隐藏元素并显示浮动按钮
        setTimeout(() => {
            aiPanel.style.display = 'none';
            
            // 如果有会话ID，显示浮动按钮
            if (floatBtn && window.currentSessionId) {
                floatBtn.style.display = 'block';
            }
        }, 300); // 与CSS动画时间一致
    }
}

// 初始化AI面板事件
function initAIPanelEvents() {
    // 关闭AI面板
    const closeBtn = document.getElementById('close-ai-btn');
    if (closeBtn) {
        closeBtn.onclick = hideAIPanel;
    }
    
    // 重新生成报告
    const regenerateBtn = document.getElementById('regenerate-btn');
    if (regenerateBtn) {
        regenerateBtn.onclick = regenerateReport;
    }
    
    // 发送消息
    const sendBtn = document.getElementById('send-btn');
    const chatInput = document.getElementById('chat-input');
    
    if (sendBtn) {
        sendBtn.onclick = sendChatMessage;
    }
    
    if (chatInput) {
        // 字符计数
        chatInput.addEventListener('input', updateCharCount);
        
        // 键盘快捷键
        chatInput.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                sendChatMessage();
            }
        });
    }
    
    // 生成最终文档
    const generateDocsBtn = document.getElementById('generate-docs-btn');
    if (generateDocsBtn) {
        generateDocsBtn.onclick = generateFinalDocuments;
    }
    
    // 语音输入（暂时显示提示）
    const voiceBtn = document.getElementById('voice-btn');
    if (voiceBtn) {
        voiceBtn.onclick = function() {
            alert('语音输入功能开发中...');
        };
    }
}

// 更新字符计数
function updateCharCount() {
    const chatInput = document.getElementById('chat-input');
    const charCount = document.getElementById('char-count');
    
    if (chatInput && charCount) {
        charCount.textContent = chatInput.value.length;
    }
}

// 显示AI状态
function showAIStatus(type, message) {
    const statusElement = document.getElementById('ai-status');
    const statusMessage = statusElement.querySelector('.status-message');
    const statusIcon = statusMessage.querySelector('.status-icon');
    const statusText = statusMessage.querySelector('.status-text');
    
    // 更新图标和文本
    if (type === 'success') {
        statusIcon.textContent = '✅';
        statusMessage.style.background = '#d4edda';
        statusMessage.style.color = '#155724';
        statusMessage.style.borderColor = '#c3e6cb';
    } else if (type === 'error') {
        statusIcon.textContent = '❌';
        statusMessage.style.background = '#f8d7da';
        statusMessage.style.color = '#721c24';
        statusMessage.style.borderColor = '#f5c6cb';
    } else if (type === 'info') {
        statusIcon.textContent = 'ℹ️';
        statusMessage.style.background = '#d1ecf1';
        statusMessage.style.color = '#0c5460';
        statusMessage.style.borderColor = '#bee5eb';
    }
    
    statusText.textContent = message;
    statusElement.style.display = 'block';
}

// 发送AI对话消息
function sendChatMessage() {
    const chatInput = document.getElementById('chat-input');
    const message = chatInput.value.trim();
    
    if (!message) {
        alert('请输入您的问题或建议');
        return;
    }
    
    if (isAIResponding) {
        alert('AI正在回复中，请稍候...');
        return;
    }
    
    // 添加用户消息到对话管理器
    const conversationId = window.conversationUI ? window.conversationUI.currentConversationId : null;
    const conversation = conversationId ? window.conversationManager.conversations.get(conversationId) : null;
    
    if (conversation) {
        window.conversationManager.addChatMessage('user', message);
    }
    
    // 添加用户消息到对话历史（向后兼容）
    addMessageToHistory('user', message);
    
    // 清空输入框
    chatInput.value = '';
    updateCharCount();
    
    // 显示AI思考状态
    isAIResponding = true;
    updateSendButtonState(true);
    
    // 发送到后端
    fetch('/api/refine_report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: window.currentSessionId,
            user_feedback: message,
            conversation_id: currentConversationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 更新对话ID
            if (data.conversation_id) {
                currentConversationId = data.conversation_id;
            }
            
            // 获取当前对话
            const conversationId = window.conversationUI ? window.conversationUI.currentConversationId : null;
            const conversation = conversationId ? window.conversationManager.conversations.get(conversationId) : null;
            
            // 如果有更新的报告数据，创建新版本
            if (data.report_data && conversation) {
                // 添加AI增强版本
                window.conversationManager.addFormVersion(conversation, data.report_data, 'ai', null, message);
                
                // 添加AI回复消息
                const lastVersion = conversation.form_versions[conversation.form_versions.length - 1];
                window.conversationManager.addChatMessage('ai', data.ai_response, 
                    conversation.form_versions.length > 1 ? conversation.form_versions[conversation.form_versions.length - 2].version_id : null,
                    lastVersion.version_id
                );
                
                showAIStatus('success', '✅ 报告已更新并创建新版本');
            } else {
                // 没有数据更新，只添加AI回复
                if (conversation) {
                    window.conversationManager.addChatMessage('ai', data.ai_response);
                }
                
                // 添加AI回复到旧的对话历史（向后兼容）
                addMessageToHistory('ai', data.ai_response);
                showAIStatus('success', '✅ 回复完成');
            }
        } else {
            // 错误处理
            const conversationId = window.conversationUI ? window.conversationUI.currentConversationId : null;
            const conversation = conversationId ? window.conversationManager.conversations.get(conversationId) : null;
            
            const errorMsg = '抱歉，处理您的请求时出现问题：' + (data.error || '未知错误');
            
            if (conversation) {
                window.conversationManager.addChatMessage('ai', errorMsg);
            }
            
            addMessageToHistory('ai', errorMsg);
            showAIStatus('error', '❌ 处理失败');
        }
    })
    .catch(error => {
        console.error('AI对话错误:', error);
        addMessageToHistory('ai', '抱歉，网络连接出现问题，请稍后重试。');
        showAIStatus('error', '❌ 网络错误');
    })
    .finally(() => {
        isAIResponding = false;
        updateSendButtonState(false);
    });
}

// 快速发送消息
function sendQuickMessage(message) {
    const chatInput = document.getElementById('chat-input');
    chatInput.value = message;
    updateCharCount();
    sendChatMessage();
}

// 添加消息到对话历史
function addMessageToHistory(type, message) {
    const chatHistory = document.getElementById('chat-history');
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'user' ? 'user-message' : 'ai-message';
    
    const avatar = type === 'user' ? '👤' : '🤖';
    
    messageDiv.innerHTML = `
        <div class="message-avatar">${avatar}</div>
        <div class="message-content">
            <p>${message.replace(/\n/g, '<br>')}</p>
        </div>
    `;
    
    chatHistory.appendChild(messageDiv);
    
    // 滚动到底部
    chatHistory.scrollTop = chatHistory.scrollHeight;
}

// 更新发送按钮状态
function updateSendButtonState(isLoading) {
    const sendBtn = document.getElementById('send-btn');
    const sendText = sendBtn.querySelector('.send-text');
    const sendLoading = sendBtn.querySelector('.send-loading');
    
    if (isLoading) {
        sendText.style.display = 'none';
        sendLoading.style.display = 'flex';
        sendBtn.disabled = true;
    } else {
        sendText.style.display = 'inline';
        sendLoading.style.display = 'none';
        sendBtn.disabled = false;
    }
}

// 重新生成报告
function regenerateReport() {
    if (isAIResponding) {
        alert('AI正在处理中，请稍候...');
        return;
    }
    
    if (!confirm('确定要重新生成报告吗？这将基于当前表单数据重新生成初始报告。')) {
        return;
    }
    
    // 收集当前表单数据
    const formData = collectFormData();
    
    isAIResponding = true;
    showAIStatus('info', 'ℹ️ 正在重新生成报告...');
    
    // 这里可以调用重新生成的API
    // 暂时模拟
    setTimeout(() => {
        addMessageToHistory('ai', '报告已重新生成，请查看更新后的内容。');
        showAIStatus('success', '✅ 报告重新生成完成');
        isAIResponding = false;
    }, 2000);
}

// 生成最终文档
function generateFinalDocuments() {
    if (isAIResponding) {
        alert('AI正在处理中，请稍候...');
        return;
    }
    
    if (!window.currentSessionId) {
        alert('会话已过期，请重新提交表单');
        return;
    }
    
    const generateBtn = document.getElementById('generate-docs-btn');
    const docsText = generateBtn.querySelector('.docs-text');
    const docsLoading = generateBtn.querySelector('.docs-loading');
    
    // 显示加载状态
    docsText.style.display = 'none';
    docsLoading.style.display = 'flex';
    generateBtn.disabled = true;
    
    showAIStatus('info', 'ℹ️ 正在生成PPT和DOCX文档...');
    
    fetch('/api/generate_final_documents', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: window.currentSessionId,
            template_version: '0610' // 可以让用户选择模板版本
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAIStatus('success', '✅ 文档生成成功！');
            
            // 显示下载链接的模态框
            showDownloadModal(data.files);
        } else {
            showAIStatus('error', '❌ 文档生成失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('文档生成错误:', error);
        showAIStatus('error', '❌ 网络错误，请稍后重试');
    })
    .finally(() => {
        // 恢复按钮状态
        docsText.style.display = 'inline';
        docsLoading.style.display = 'none';
        generateBtn.disabled = false;
    });
}



// 显示下载模态框
function showDownloadModal(files) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 12px;
        max-width: 400px;
        width: 90%;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    `;
    
    content.innerHTML = `
        <h3 style="margin: 0 0 20px 0; color: #333;">🎉 文档生成成功</h3>
        <p style="margin: 0 0 20px 0; color: #666;">您的8D报告已生成完成，请下载：</p>
        
        <!-- 下载状态显示区域 -->
        <div id="download-status" style="margin: 10px 0; padding: 10px; border-radius: 6px; display: none;">
            <div id="download-message" style="margin-bottom: 8px;"></div>
            <div id="download-progress" style="width: 100%; height: 4px; background: #f0f0f0; border-radius: 2px; overflow: hidden;">
                <div id="download-progress-bar" style="height: 100%; background: #4a90e2; width: 0%; transition: width 0.3s;"></div>
            </div>
        </div>
        
        <div id="download-buttons" style="display: flex; flex-direction: column; gap: 10px; margin-bottom: 20px;">
            ${files.ppt ? `<button onclick="downloadFile('${files.ppt}', 'PPT')" style="background: #4a90e2; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 8px; transition: background 0.3s;" onmouseover="this.style.background='#357abd'" onmouseout="this.style.background='#4a90e2'">📊 下载PPT文件</button>` : ''}
            ${files.docx ? `<button onclick="downloadFile('${files.docx}', 'DOCX')" style="background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 8px; transition: background 0.3s;" onmouseover="this.style.background='#218838'" onmouseout="this.style.background='#28a745'">📄 下载DOCX文件</button>` : ''}
        </div>
        <button onclick="this.closest('.modal').remove()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: background 0.3s;" onmouseover="this.style.background='#5a6268'" onmouseout="this.style.background='#6c757d'">关闭</button>
    `;
    
    modal.className = 'modal';
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 在模态框中下载文件并显示状态
function downloadFile(url, fileType) {
    const statusDiv = document.getElementById('download-status');
    const messageDiv = document.getElementById('download-message');
    const progressBar = document.getElementById('download-progress-bar');
    
    // 显示下载状态
    statusDiv.style.display = 'block';
    statusDiv.style.background = '#e7f3ff';
    statusDiv.style.border = '1px solid #4a90e2';
    messageDiv.innerHTML = `<span style="color: #4a90e2;">🔄 正在下载${fileType}文件...</span>`;
    progressBar.style.width = '30%';
    
    // 创建隐藏的下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = ''; // 让浏览器决定文件名
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    
    // 模拟下载进度
    let progress = 30;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);
    
    // 触发下载
    downloadLink.click();
    
    // 下载开始后，模拟完成状态
    setTimeout(() => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        statusDiv.style.background = '#d4edda';
        statusDiv.style.border = '1px solid #28a745';
        messageDiv.innerHTML = `<span style="color: #28a745;">✅ ${fileType}文件下载完成！</span>`;
        
        // 3秒后隐藏状态信息
        setTimeout(() => {
            statusDiv.style.display = 'none';
            progressBar.style.width = '0%';
        }, 3000);
    }, 1500);
    
    // 清理临时下载链接
    setTimeout(() => {
        document.body.removeChild(downloadLink);
    }, 2000);
}

// 从数据填充表单
function populateFormFromData(data) {
    console.log('Populating form with data:', data);
    
    if (!data || typeof data !== 'object') {
        console.log('Invalid data for form population');
        return;
    }
    
    // 如果数据中包含report_data，则使用report_data
    if (data.report_data) {
        data = data.report_data;
    }
    
    // 实用函数：设置字段值
    function setFieldValue(fieldId, value) {
        const element = document.getElementById(fieldId);
        if (element && value && value.trim() !== '') {
            element.value = value;
            if (element.tagName === 'TEXTAREA') {
                autoResizeTextarea(element);
            }
            console.log(`Updated field ${fieldId}: "${value}"`);
        }
    }
    
    // 实用函数：从${key}格式提取值
    function extractValue(obj, key) {
        const dollarKey = `\${${key}}`;
        return obj[dollarKey] || obj[key] || '';
    }
    
    // 处理D0汇报信息
    if (data.D0汇报信息 || data['D0汇报信息']) {
        const d0Data = data.D0汇报信息 || data['D0汇报信息'];
        setFieldValue('d0_title', extractValue(d0Data, 'D0标题'));
        setFieldValue('d0_reporter', extractValue(d0Data, 'D0汇报人'));
        setFieldValue('d0_time', extractValue(d0Data, 'D0汇报时间'));
        setFieldValue('d0_background', extractValue(d0Data, 'D0项目背景'));
    }
    
    // 处理D1建立小组
    if (data.D1建立小组 || data['D1建立小组']) {
        const d1Data = data.D1建立小组 || data['D1建立小组'];
        
        // 处理组长信息
        if (d1Data.组长) {
            const leaderData = d1Data.组长;
            setFieldValue('d1_leader_name', extractValue(leaderData, 'D1组长姓名'));
            setFieldValue('d1_leader_dept', extractValue(leaderData, 'D1组长部门'));
            setFieldValue('d1_leader_position', extractValue(leaderData, 'D1组长职位'));
            setFieldValue('d1_leader_responsibility', extractValue(leaderData, 'D1组长主要职责'));
        }
        
        // 处理成员信息
        for (let i = 1; i <= 10; i++) {
            const memberKey = `成员${i}`;
            if (d1Data[memberKey]) {
                const memberData = d1Data[memberKey];
                setFieldValue(`d1_member${i}_name`, extractValue(memberData, `D1成员${i}姓名`));
                setFieldValue(`d1_member${i}_dept`, extractValue(memberData, `D1成员${i}部门`));
                setFieldValue(`d1_member${i}_position`, extractValue(memberData, `D1成员${i}职位`));
                setFieldValue(`d1_member${i}_responsibility`, extractValue(memberData, `D1成员${i}主要职责`));
            }
        }
    }
    
    // 处理D2问题描述
    if (data.D2问题描述 || data['D2问题描述']) {
        const d2Data = data.D2问题描述 || data['D2问题描述'];
        setFieldValue('d2_description', extractValue(d2Data, 'D2事件整体描述'));
        
        // 处理5W2H
        if (d2Data['5W2H']) {
            const w2hData = d2Data['5W2H'];
            setFieldValue('d2_when', extractValue(w2hData, 'D2何时发生'));
            setFieldValue('d2_where', extractValue(w2hData, 'D2何地发生'));
            setFieldValue('d2_who', extractValue(w2hData, 'D2何人发现'));
            setFieldValue('d2_what', extractValue(w2hData, 'D2发生了什么问题'));
            setFieldValue('d2_why', extractValue(w2hData, 'D2为什么是这问题'));
            setFieldValue('d2_how', extractValue(w2hData, 'D2问题如何发生'));
            setFieldValue('d2_impact', extractValue(w2hData, 'D2问题影响程度'));
        }
    }
    
    // 处理D3临时措施
    if (data.D3临时措施 || data['D3临时措施']) {
        const d3Data = data.D3临时措施 || data['D3临时措施'];
        
        for (let i = 1; i <= 10; i++) {
            const measureKey = `临时措施${i}`;
            if (d3Data[measureKey]) {
                const measureData = d3Data[measureKey];
                setFieldValue(`d3_scope${i}`, extractValue(measureData, `D3范围${i}`));
                setFieldValue(`d3_measure${i}`, extractValue(measureData, `D3处置对策${i}`));
                setFieldValue(`d3_responsible${i}`, extractValue(measureData, `D3责任人${i}`));
                setFieldValue(`d3_deadline${i}`, extractValue(measureData, `D3完成期限${i}`));
                setFieldValue(`d3_status${i}`, extractValue(measureData, `D3状态${i}`));
                setFieldValue(`d3_note${i}`, extractValue(measureData, `D3进度备注${i}`));
            }
        }
    }
    
    // 处理D4根本原因
    if (data.D4根本原因 || data['D4根本原因']) {
        const d4Data = data.D4根本原因 || data['D4根本原因'];
        
        // 处理5why分析
        if (d4Data['5why分析']) {
            const whyData = d4Data['5why分析'];
            setFieldValue('d4_why1', extractValue(whyData, 'D4why1'));
            setFieldValue('d4_answer1', extractValue(whyData, 'D4answer1'));
            setFieldValue('d4_why2', extractValue(whyData, 'D4why2'));
            setFieldValue('d4_answer2', extractValue(whyData, 'D4answer2'));
            setFieldValue('d4_why3', extractValue(whyData, 'D4why3'));
            setFieldValue('d4_answer3', extractValue(whyData, 'D4answer3'));
            setFieldValue('d4_why4', extractValue(whyData, 'D4why4'));
            setFieldValue('d4_answer4', extractValue(whyData, 'D4answer4'));
            setFieldValue('d4_why5', extractValue(whyData, 'D4why5'));
            setFieldValue('d4_answer5', extractValue(whyData, 'D4answer5'));
        }
        
                 // 处理人机料法环测分析
         if (d4Data['人机料法环测分析']) {
             const analysisData = d4Data['人机料法环测分析'];
             
             // 映射表：中文到英文字段名的映射
             const categoryMapping = {
                 '人': 'man',
                 '机': 'machine',
                 '料': 'material',
                 '法': 'method',
                 '环': 'environment',
                 '测': 'measurement'
             };
             
             Object.keys(categoryMapping).forEach(category => {
                 const categoryKey = `${category}原因`;
                 const englishCategory = categoryMapping[category];

                 if (analysisData[categoryKey] && Array.isArray(analysisData[categoryKey])) {
                     analysisData[categoryKey].forEach((item, index) => {
                         const num = index + 1;
                         setFieldValue(`d4_${englishCategory}${num}`, extractValue(item, `D4${category}原因${num}`));
                         // 使用新的键名格式：${D4人判定1}、${D4人证据1}
                         setFieldValue(`d4_${englishCategory}${num}_judgment`, extractValue(item, `D4${category}判定${num}`));
                         setFieldValue(`d4_${englishCategory}${num}_evidence`, extractValue(item, `D4${category}证据${num}`));
                     });
                 }
             });
         }
        
        // 处理原因小结
        setFieldValue('d4_summary', extractValue(d4Data, 'D4原因小结'));
    }
    
    // 处理D5永久措施
    if (data.D5永久措施 || data['D5永久措施']) {
        const d5Data = data.D5永久措施 || data['D5永久措施'];
        
        for (let i = 1; i <= 10; i++) {
            const measureKey = `措施${i}`;
            if (d5Data[measureKey]) {
                const measureData = d5Data[measureKey];
                setFieldValue(`d5_measure${i}`, extractValue(measureData, `D5纠正措施${i}`));
                setFieldValue(`d5_responsible${i}`, extractValue(measureData, `D5责任人${i}`));
                setFieldValue(`d5_deadline${i}`, extractValue(measureData, `D5计划完成日期${i}`));
            }
        }
    }
    
    // 处理D6措施验证
    if (data.D6措施验证 || data['D6措施验证']) {
        const d6Data = data.D6措施验证 || data['D6措施验证'];
        
        for (let i = 1; i <= 10; i++) {
            const verificationKey = `验证${i}`;
            if (d6Data[verificationKey]) {
                const verificationData = d6Data[verificationKey];
                setFieldValue(`d6_verification${i}`, extractValue(verificationData, `D6措施验证${i}`));
                setFieldValue(`d6_verifier${i}`, extractValue(verificationData, `D6验证人${i}`));
                setFieldValue(`d6_time${i}`, extractValue(verificationData, `D6验证时间${i}`));
                setFieldValue(`d6_result${i}`, extractValue(verificationData, `D6验证结果${i}`));
            }
        }
    }
    
    // 处理D7预防措施
    if (data.D7预防措施 || data['D7预防措施']) {
        const d7Data = data.D7预防措施 || data['D7预防措施'];
        
        for (let i = 1; i <= 10; i++) {
            const preventionKey = `预防${i}`;
            if (d7Data[preventionKey]) {
                const preventionData = d7Data[preventionKey];
                setFieldValue(`d7_prevention${i}`, extractValue(preventionData, `D7预防措施${i}`));
                setFieldValue(`d7_responsible${i}`, extractValue(preventionData, `D7责任人${i}`));
                setFieldValue(`d7_deadline${i}`, extractValue(preventionData, `D7计划完成日期${i}`));
            }
        }
    }
    
    // 处理D8庆贺团队
    if (data.D8庆贺团队 || data['D8庆贺团队']) {
        const d8Data = data.D8庆贺团队 || data['D8庆贺团队'];
        setFieldValue('d8_effectiveness', extractValue(d8Data, 'D8有效性确认'));
        setFieldValue('d8_confirmer', extractValue(d8Data, 'D8确认人'));
        setFieldValue('d8_confirm_time', extractValue(d8Data, 'D8确认完成时间'));
    }
    
    // 更新进度和计数器
    updateProgress();
    updateMemberCount();
    updateMeasureCount();
    updateD5MeasureCount();
    updateD6VerificationCount();
    updateD7PreventionCount();
}



// 将表单数据转换为模板处理格式
function convertFormDataToTemplateFormat(formData) {
    // 基础字段映射
    const baseFieldMapping = {
        // D0 基本信息
        'd0_title': '${D0标题}',
        'd0_reporter': '${D0汇报人}',
        'd0_time': '${D0汇报时间}',
        'd0_background': '${D0项目背景}',
        
        // D1 组长信息
        'd1_leader_name': '${D1组长姓名}',
        'd1_leader_dept': '${D1组长部门}',
        'd1_leader_position': '${D1组长职位}',
        'd1_leader_responsibility': '${D1组长主要职责}',
        
        // D2 问题描述
        'd2_description': '${D2事件整体描述}',
        'd2_when': '${D2何时发生}',
        'd2_where': '${D2何地发生}',
        'd2_who': '${D2何人发现}',
        'd2_what': '${D2发生了什么问题}',
        'd2_why': '${D2为什么是这问题}',
        'd2_how': '${D2问题如何发生}',
        'd2_impact': '${D2问题影响程度}',
        
        // D4 5Why分析
        'd4_why1': '${D4why1}',
        'd4_answer1': '${D4answer1}',
        'd4_why2': '${D4why2}',
        'd4_answer2': '${D4answer2}',
        'd4_why3': '${D4why3}',
        'd4_answer3': '${D4answer3}',
        'd4_why4': '${D4why4}',
        'd4_answer4': '${D4answer4}',
        'd4_why5': '${D4why5}',
        'd4_answer5': '${D4answer5}',
        'd4_summary': '${D4原因小结}',
        
        // D8 庆贺团队
        'd8_effectiveness': '${D8有效性确认}',
        'd8_confirmer': '${D8确认人}',
        'd8_confirm_time': '${D8确认完成时间}'
    };
    
    // 构建基础JSON数据结构
    const jsonData = {
        "template_name": "template.pptx",
        "replacements": {
            "D0汇报信息": {
                [baseFieldMapping['d0_title']]: formData['d0_title'] || '',
                [baseFieldMapping['d0_reporter']]: formData['d0_reporter'] || '',
                [baseFieldMapping['d0_time']]: formData['d0_time'] || '',
                [baseFieldMapping['d0_background']]: formData['d0_background'] || ''
            },
            "D1建立小组": {
                "组长": {
                    [baseFieldMapping['d1_leader_name']]: formData['d1_leader_name'] || '',
                    [baseFieldMapping['d1_leader_dept']]: formData['d1_leader_dept'] || '',
                    [baseFieldMapping['d1_leader_position']]: formData['d1_leader_position'] || '',
                    [baseFieldMapping['d1_leader_responsibility']]: formData['d1_leader_responsibility'] || ''
                }
            },
            "D2问题描述": {
                [baseFieldMapping['d2_description']]: formData['d2_description'] || '',
                "5W2H": {
                    [baseFieldMapping['d2_when']]: formData['d2_when'] || '',
                    [baseFieldMapping['d2_where']]: formData['d2_where'] || '',
                    [baseFieldMapping['d2_who']]: formData['d2_who'] || '',
                    [baseFieldMapping['d2_why']]: formData['d2_why'] || '',
                    [baseFieldMapping['d2_what']]: formData['d2_what'] || '',
                    [baseFieldMapping['d2_how']]: formData['d2_how'] || '',
                    [baseFieldMapping['d2_impact']]: formData['d2_impact'] || ''
                }
            },
            "D3临时措施": {},
            "D4根本原因": {
                "5why分析": {
                    [baseFieldMapping['d4_why1']]: formData['d4_why1'] || '',
                    [baseFieldMapping['d4_answer1']]: formData['d4_answer1'] || '',
                    [baseFieldMapping['d4_why2']]: formData['d4_why2'] || '',
                    [baseFieldMapping['d4_answer2']]: formData['d4_answer2'] || '',
                    [baseFieldMapping['d4_why3']]: formData['d4_why3'] || '',
                    [baseFieldMapping['d4_answer3']]: formData['d4_answer3'] || '',
                    [baseFieldMapping['d4_why4']]: formData['d4_why4'] || '',
                    [baseFieldMapping['d4_answer4']]: formData['d4_answer4'] || '',
                    [baseFieldMapping['d4_why5']]: formData['d4_why5'] || '',
                    [baseFieldMapping['d4_answer5']]: formData['d4_answer5'] || ''
                },
                "人机料法环测分析": {},
                [baseFieldMapping['d4_summary']]: formData['d4_summary'] || ''
            },
            "D5永久措施": {},
            "D6措施验证": {},
            "D7预防措施": {},
            "D8庆贺团队": {
                [baseFieldMapping['d8_effectiveness']]: formData['d8_effectiveness'] || '',
                [baseFieldMapping['d8_confirmer']]: formData['d8_confirmer'] || '',
                [baseFieldMapping['d8_confirm_time']]: formData['d8_confirm_time'] || ''
            }
        }
    };
    
    // 添加D1成员信息
    for (let memberNum = 1; memberNum <= 2; memberNum++) {
        jsonData.replacements["D1建立小组"][`成员${memberNum}`] = {
            [`\${D1成员${memberNum}姓名}`]: formData[`d1_member${memberNum}_name`] || '',
            [`\${D1成员${memberNum}部门}`]: formData[`d1_member${memberNum}_dept`] || '',
            [`\${D1成员${memberNum}职位}`]: formData[`d1_member${memberNum}_position`] || '',
            [`\${D1成员${memberNum}主要职责}`]: formData[`d1_member${memberNum}_responsibility`] || ''
        };
    }
    
    // 动态添加额外的成员信息
    const memberNumbers = new Set();
    Object.keys(formData).forEach(key => {
        if (key.startsWith('d1_member') && key.endsWith('_name')) {
            const match = key.match(/d1_member(\d+)_name/);
            if (match) {
                const memberNum = parseInt(match[1]);
                if (memberNum > 2) {
                    memberNumbers.add(memberNum);
                }
            }
        }
    });
    
    Array.from(memberNumbers).sort().forEach(i => {
        jsonData.replacements["D1建立小组"][`成员${i}`] = {
            [`\${D1成员${i}姓名}`]: formData[`d1_member${i}_name`] || '',
            [`\${D1成员${i}部门}`]: formData[`d1_member${i}_dept`] || '',
            [`\${D1成员${i}职位}`]: formData[`d1_member${i}_position`] || '',
            [`\${D1成员${i}主要职责}`]: formData[`d1_member${i}_responsibility`] || ''
        };
    });
    
    // 添加D3临时措施信息
    jsonData.replacements["D3临时措施"]["临时措施1"] = {
        "${D3范围1}": formData['d3_scope1'] || '',
        "${D3处置对策1}": formData['d3_measure1'] || '',
        "${D3责任人1}": formData['d3_responsible1'] || '',
        "${D3完成期限1}": formData['d3_deadline1'] || '',
        "${D3状态1}": formData['d3_status1'] || '',
        "${D3进度备注1}": formData['d3_note1'] || ''
    };
    
    // 动态添加额外的D3措施
    const measureNumbers = new Set();
    Object.keys(formData).forEach(key => {
        if (key.startsWith('d3_scope') && key.length > 8) {
            const numStr = key.substring(8);
            if (/^\d+$/.test(numStr)) {
                const measureNum = parseInt(numStr);
                if (measureNum > 1) {
                    measureNumbers.add(measureNum);
                }
            }
        }
    });
    
    Array.from(measureNumbers).sort().forEach(i => {
        jsonData.replacements["D3临时措施"][`临时措施${i}`] = {
            [`\${D3范围${i}}`]: formData[`d3_scope${i}`] || '',
            [`\${D3处置对策${i}}`]: formData[`d3_measure${i}`] || '',
            [`\${D3责任人${i}}`]: formData[`d3_responsible${i}`] || '',
            [`\${D3完成期限${i}}`]: formData[`d3_deadline${i}`] || '',
            [`\${D3状态${i}}`]: formData[`d3_status${i}`] || '',
            [`\${D3进度备注${i}}`]: formData[`d3_note${i}`] || ''
        };
    });
    
    // 添加D4人机料法环测原因分析
    const causeCategories = {
        'man': '人原因',
        'machine': '机原因',
        'material': '料原因',
        'method': '法原因',
        'environment': '环原因',
        'measurement': '测原因'
    };
    
    Object.keys(causeCategories).forEach(category => {
        const label = causeCategories[category];
        const causeNumbers = new Set([1]); // 确保至少包含原因1
        
        Object.keys(formData).forEach(key => {
            if (key.startsWith(`d4_${category}`) && !key.endsWith('_judgment') && !key.endsWith('_evidence')) {
                const suffix = key.substring(`d4_${category}`.length);
                if (/^\d+$/.test(suffix)) {
                    causeNumbers.add(parseInt(suffix));
                }
            }
        });
        
        const causesArray = [];
        Array.from(causeNumbers).sort().forEach(i => {
            // 提取类别名称（去掉"原因"）
            const categoryName = label.replace('原因', '');
            causesArray.push({
                [`\${D4${label}${i}}`]: formData[`d4_${category}${i}`] || '',
                [`\${D4${categoryName}判定${i}}`]: formData[`d4_${category}${i}_judgment`] || '',
                [`\${D4${categoryName}证据${i}}`]: formData[`d4_${category}${i}_evidence`] || ''
            });
        });
        
        jsonData.replacements["D4根本原因"]["人机料法环测分析"][label] = causesArray;
    });
    
    // 添加D5永久措施
    jsonData.replacements["D5永久措施"]["措施1"] = {
        "${D5纠正措施1}": formData['d5_measure1'] || '',
        "${D5责任人1}": formData['d5_responsible1'] || '',
        "${D5计划完成日期1}": formData['d5_deadline1'] || ''
    };
    
    const d5MeasureNumbers = new Set();
    Object.keys(formData).forEach(key => {
        if (key.startsWith('d5_measure') && key.length > 10) {
            const numStr = key.substring(10);
            if (/^\d+$/.test(numStr)) {
                const measureNum = parseInt(numStr);
                if (measureNum > 1) {
                    d5MeasureNumbers.add(measureNum);
                }
            }
        }
    });
    
    Array.from(d5MeasureNumbers).sort().forEach(i => {
        jsonData.replacements["D5永久措施"][`措施${i}`] = {
            [`\${D5纠正措施${i}}`]: formData[`d5_measure${i}`] || '',
            [`\${D5责任人${i}}`]: formData[`d5_responsible${i}`] || '',
            [`\${D5计划完成日期${i}}`]: formData[`d5_deadline${i}`] || ''
        };
    });
    
    // 添加D6措施验证
    jsonData.replacements["D6措施验证"]["验证1"] = {
        "${D6措施验证1}": formData['d6_verification1'] || '',
        "${D6验证人1}": formData['d6_verifier1'] || '',
        "${D6验证时间1}": formData['d6_time1'] || '',
        "${D6验证结果1}": formData['d6_result1'] || ''
    };
    
    const d6VerificationNumbers = new Set();
    Object.keys(formData).forEach(key => {
        if (key.startsWith('d6_verification') && key.length > 15) {
            const numStr = key.substring(15);
            if (/^\d+$/.test(numStr)) {
                const verificationNum = parseInt(numStr);
                if (verificationNum > 1) {
                    d6VerificationNumbers.add(verificationNum);
                }
            }
        }
    });
    
    Array.from(d6VerificationNumbers).sort().forEach(i => {
        jsonData.replacements["D6措施验证"][`验证${i}`] = {
            [`\${D6措施验证${i}}`]: formData[`d6_verification${i}`] || '',
            [`\${D6验证人${i}}`]: formData[`d6_verifier${i}`] || '',
            [`\${D6验证时间${i}}`]: formData[`d6_time${i}`] || '',
            [`\${D6验证结果${i}}`]: formData[`d6_result${i}`] || ''
        };
    });
    
    // 添加D7预防措施
    jsonData.replacements["D7预防措施"]["预防1"] = {
        "${D7预防措施1}": formData['d7_prevention1'] || '',
        "${D7责任人1}": formData['d7_responsible1'] || '',
        "${D7计划完成日期1}": formData['d7_deadline1'] || ''
    };
    
    const d7PreventionNumbers = new Set();
    Object.keys(formData).forEach(key => {
        if (key.startsWith('d7_prevention') && key.length > 13) {
            const numStr = key.substring(13);
            if (/^\d+$/.test(numStr)) {
                const preventionNum = parseInt(numStr);
                if (preventionNum > 1) {
                    d7PreventionNumbers.add(preventionNum);
                }
            }
        }
    });
    
    Array.from(d7PreventionNumbers).sort().forEach(i => {
        jsonData.replacements["D7预防措施"][`预防${i}`] = {
            [`\${D7预防措施${i}}`]: formData[`d7_prevention${i}`] || '',
            [`\${D7责任人${i}}`]: formData[`d7_responsible${i}`] || '',
            [`\${D7计划完成日期${i}}`]: formData[`d7_deadline${i}`] || ''
        };
    });
    
    return jsonData;
}



// ==================== AI协作功能结束 ====================

// ==================== 对话式界面初始化 ====================

/**
 * 初始化对话式界面
 */
function initConversationInterface() {
    try {
        // 等待对话管理器和UI初始化完成
        if (window.conversationManager && window.conversationUI) {
            // 检查是否有现有对话
            const conversations = window.conversationManager.getAllConversations();
            
            if (conversations.length === 0) {
                // 没有现有对话，等待用户开始填写时再创建
                console.log('没有现有对话，等待用户开始填写');
                // 隐藏版本标签栏，等待用户输入后显示
                window.conversationUI.hideVersionTabs();
                window.conversationUI.updateConversationTitle('8D报告协作系统');
                window.conversationUI.updateConversationSubtitle('智能协作，优化报告质量');
            } else {
                // 加载最近的对话
                const latestConversation = conversations[0];
                console.log('加载最近的对话:', latestConversation.title);
                window.conversationUI.loadConversation(latestConversation.conversation_id);
            }
        } else {
            // 如果对话管理器未就绪，稍后重试
            setTimeout(initConversationInterface, 100);
        }
    } catch (error) {
        console.error('初始化对话界面失败:', error);
        // 回退到传统模式
        updateRecordsList();
    }
}

/**
 * 确保第一个版本存在
 * 当用户开始填写表单时，自动创建"原始输入"版本
 */
function ensureFirstVersionExists() {
    // 如果正在清空表单，跳过
    if (window.isFormClearing) {
        return;
    }
    
    // 检查是否有对话管理器和UI
    if (!window.conversationManager || !window.conversationUI) {
        return;
    }
    
    // 如果没有当前对话，创建新对话
    if (!window.conversationUI.currentConversationId) {
        console.log('用户开始填写表单，创建新对话');
        const formData = collectFormData();
        
        // 创建新对话，即使表单是空的也创建
        const conversationId = window.conversationManager.createConversation(null, '新建8D报告');
        window.conversationUI.currentConversationId = conversationId;
        
        // 手动创建"原始输入"版本
        const conversation = window.conversationManager.conversations.get(conversationId);
        if (conversation) {
            window.conversationManager.addFormVersion(conversation, formData, 'user');
            console.log('已创建原始输入版本');
            
            // 通知UI更新
            if (window.conversationUI) {
                window.conversationUI.renderVersionTabs(conversation);
                window.conversationUI.showVersionTabs();
                window.conversationUI.updateConversationTitle('新建8D报告');
                window.conversationUI.updateConversationSubtitle('正在编辑原始输入');
            }
        }
    }
    // 如果有对话但没有版本，创建第一个版本
    else {
        const conversation = window.conversationManager.conversations.get(window.conversationUI.currentConversationId);
        if (conversation && conversation.form_versions.length === 0) {
            console.log('对话存在但无版本，创建原始输入版本');
            const formData = collectFormData();
            window.conversationManager.addFormVersion(conversation, formData, 'user');
            
            // 通知UI更新
            if (window.conversationUI) {
                window.conversationUI.renderVersionTabs(conversation);
                window.conversationUI.showVersionTabs();
            }
        }
    }
}

/**
 * 向后兼容的自动保存功能
 */
function autoSave() {
    // 如果正在清空表单，跳过自动保存
    if (window.isFormClearing) {
        console.log('表单清空中，跳过自动保存');
        return;
    }
    
    if (window.conversationManager && window.conversationUI && window.conversationUI.currentConversationId) {
        // 使用对话管理器的自动保存
        const formData = collectFormData();
        if (!window.conversationManager.isFormEmpty(formData)) {
            const conversation = window.conversationManager.conversations.get(window.conversationUI.currentConversationId);
            if (conversation) {
                // 更新当前活跃版本的数据
                const activeVersion = conversation.form_versions.find(v => v.is_active);
                if (activeVersion) {
                    activeVersion.form_data = formData;
                    activeVersion.created_at = new Date().toISOString();
                    conversation.updated_at = new Date().toISOString();
                    window.conversationManager.saveConversationsToStorage();
                }
            }
        }
    } else {
        // 回退到传统的自动保存逻辑
        if (typeof originalAutoSave === 'function') {
            originalAutoSave();
        }
    }
}

/**
 * 初始化滚轮控制
 */
function initWheelControl() {
    document.addEventListener('wheel', function(e) {
        const target = e.target;
        const sidebar = document.getElementById('unified-sidebar');
        const rightNav = document.getElementById('right-navigation');

        // 检查鼠标是否在侧边栏内
        if (sidebar && sidebar.contains(target)) {
            // 找到最近的可滚动元素
            let scrollableElement = target;
            while (scrollableElement && scrollableElement !== sidebar) {
                const computedStyle = window.getComputedStyle(scrollableElement);
                const overflowY = computedStyle.overflowY;

                if (overflowY === 'auto' || overflowY === 'scroll') {
                    break;
                }
                scrollableElement = scrollableElement.parentElement;
            }

            // 如果没找到可滚动元素，查找默认的滚动容器
            if (!scrollableElement || scrollableElement === sidebar) {
                // 优先检查版本记录区域是否可见且可滚动
                const versionList = sidebar.querySelector('.version-list');
                const conversationList = sidebar.querySelector('.conversation-list');

                if (versionList && versionList.offsetParent !== null &&
                    versionList.scrollHeight > versionList.clientHeight) {
                    scrollableElement = versionList;
                } else if (conversationList && conversationList.offsetParent !== null &&
                          conversationList.scrollHeight > conversationList.clientHeight) {
                    scrollableElement = conversationList;
                } else {
                    // 没有找到需要滚动的元素，允许事件传播到主页面
                    return;
                }
            }

            // 如果找到了可滚动元素，检查滚动边界
            if (scrollableElement && scrollableElement !== sidebar) {
                const scrollTop = scrollableElement.scrollTop;
                const scrollHeight = scrollableElement.scrollHeight;
                const clientHeight = scrollableElement.clientHeight;

                // 检查是否在滚动边界
                const isAtTop = scrollTop === 0;
                const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;

                // 如果在边界且滚动方向会导致溢出，则阻止默认行为
                if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }

                // 允许正常滚动，但阻止事件冒泡到主页面
                e.stopPropagation();
                return;
            }

            // 没有找到合适的滚动元素，允许事件传播
            return;
        }

        // 检查鼠标是否在右侧导航栏内
        if (rightNav && rightNav.contains(target)) {
            e.preventDefault();

            // 获取当前活跃的章节
            const activeSection = document.querySelector('.nav-section.active');
            if (!activeSection) return;

            const sections = Array.from(document.querySelectorAll('.nav-section'));
            const currentIndex = sections.indexOf(activeSection);

            let targetIndex;
            if (e.deltaY > 0) {
                // 向下滚动，跳转到下一个章节
                targetIndex = Math.min(currentIndex + 1, sections.length - 1);
            } else {
                // 向上滚动，跳转到上一个章节
                targetIndex = Math.max(currentIndex - 1, 0);
            }

            if (targetIndex !== currentIndex) {
                const targetSection = sections[targetIndex];
                const sectionName = targetSection.dataset.section;

                // 跳转到目标章节
                navigateToSection(sectionName);
            }

            return;
        }

        // 在其他区域，允许正常的页面滚动
    }, { passive: false });
}



// 页面加载完成后初始化所有功能
document.addEventListener('DOMContentLoaded', function() {
    initAutoResize();
    
    // 初始化对话式界面
    initConversationInterface();
    
    // 初始化进度和导航
    updateProgress();
    
    // 延迟初始化导航，确保页面布局完成
    setTimeout(function() {
        try {
            initNavigation();
            initScrollTracking();
            initWheelControl();

            // 确保初始状态正确
            const initialSection = getCurrentVisibleSection();
            setActiveSection(initialSection);
        } catch (e) {
            // 静默处理初始化错误
        }
    }, 300);
    
    // 绑定表单提交事件
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // 绑定所有输入字段的change事件来自动保存和更新进度（包括动态添加的字段）
    const mainForm = document.querySelector('form');
    mainForm.addEventListener('input', (e) => {
        if (e.target.matches('input, textarea, select')) {
            // 检查是否需要创建第一个版本
            ensureFirstVersionExists();
            autoSave();
            updateProgress();
        }
    });
    mainForm.addEventListener('change', (e) => {
        if (e.target.matches('input, textarea, select')) {
            // 检查是否需要创建第一个版本
            ensureFirstVersionExists();
            autoSave();
            updateProgress();
        }
    });
    
    // 页面卸载时提示 - 只在关键操作进行中才提示
    window.addEventListener('beforeunload', function(event) {
        // 只在关键操作进行时才阻止离开
        if (isSubmitting) {
            const message = '表单正在提交中，请稍候...';
            event.returnValue = message;
            return message;
        }
        
        if (isAIResponding) {
            const message = 'AI正在处理中，请稍候...';
            event.returnValue = message;
            return message;
        }
        
        // 其他情况下不阻止离开页面（因为有自动保存功能）
    });
    
    // 监听页面刷新 (F5 或 Ctrl+R) - 由于有自动保存功能，只需提醒用户
    document.addEventListener('keydown', function(event) {
        if ((event.key === 'F5') || (event.ctrlKey && event.key === 'r')) {
            const formData = collectFormData();
            const hasData = !isFormEmpty(formData);
            
            if (hasData) {
                // 由于有自动保存功能，数据已经保存，只需简单确认
                const confirmed = confirm('您的数据已自动保存。确定要刷新页面吗？');
                if (!confirmed) {
                    event.preventDefault();
                }
            }
        }
    });

    // 初始化新的按钮事件监听器
    initNewActionButtons();
    initAIOptimizeModal();
}); 

// 新的按钮事件处理
function initNewActionButtons() {
    const aiOptimizeBtn = document.getElementById('ai-optimize-btn');
    const directGenerateBtn = document.getElementById('direct-generate-btn');

    if (aiOptimizeBtn) {
        aiOptimizeBtn.addEventListener('click', function() {
            // 验证必填字段
            const missingFields = validateRequiredFields();
            if (missingFields.length > 0) {
                alert(`请填写以下必填字段：\n${missingFields.join('\n')}`);
                return;
            }
            
            // 确保有第一个版本存在
            ensureFirstVersionExists();
            
            // 显示AI优化模态框
            showAIOptimizeModal();
        });
    }

    if (directGenerateBtn) {
        directGenerateBtn.addEventListener('click', function() {
            // 验证必填字段
            const missingFields = validateRequiredFields();
            if (missingFields.length > 0) {
                alert(`请填写以下必填字段：\n${missingFields.join('\n')}`);
                return;
            }
            
            // 直接生成报告
            generateDirectReport();
        });
    }
    

}

// AI优化模态框相关功能
function initAIOptimizeModal() {
    const modal = document.getElementById('ai-optimize-modal');
    const closeBtn = document.getElementById('modal-close-btn');
    const cancelBtn = document.getElementById('modal-cancel-btn');
    const confirmBtn = document.getElementById('modal-confirm-btn');
    const textarea = document.getElementById('optimize-requirements');
    const charCount = document.getElementById('optimize-char-count');

    // 关闭模态框事件
    if (closeBtn) {
        closeBtn.addEventListener('click', hideAIOptimizeModal);
    }
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', hideAIOptimizeModal);
    }

    // 点击背景关闭模态框
    if (modal) {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                hideAIOptimizeModal();
            }
        });
    }

    // 字符计数
    if (textarea && charCount) {
        textarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            charCount.textContent = currentLength;
            
            if (currentLength > 500) {
                charCount.style.color = '#dc3545';
            } else {
                charCount.style.color = '#666';
            }
        });
    }

    // 确认AI优化
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            const requirements = textarea ? textarea.value.trim() : '';
            hideAIOptimizeModal();
            startAIOptimization(requirements);
        });
    }

    // 回车键发送
    if (textarea) {
        textarea.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                confirmBtn.click();
            }
        });
    }
}

function showAIOptimizeModal() {
    const modal = document.getElementById('ai-optimize-modal');
    const textarea = document.getElementById('optimize-requirements');
    
    if (modal) {
        modal.style.display = 'flex';
        // 清空之前的内容
        if (textarea) {
            textarea.value = '';
            textarea.focus();
        }
        // 重置字符计数
        const charCount = document.getElementById('optimize-char-count');
        if (charCount) {
            charCount.textContent = '0';
            charCount.style.color = '#666';
        }
    }
}

function hideAIOptimizeModal() {
    const modal = document.getElementById('ai-optimize-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// AI优化处理
function startAIOptimization(requirements) {
    const aiOptimizeBtn = document.getElementById('ai-optimize-btn');
    const btnText = aiOptimizeBtn.querySelector('.btn-text');
    const btnLoading = aiOptimizeBtn.querySelector('.btn-loading');
    
    // 显示加载状态
    btnText.style.display = 'none';
    btnLoading.style.display = 'flex';
    aiOptimizeBtn.disabled = true;
    
    // 收集表单数据
    const formData = collectFormData();
    const templateData = convertFormDataToTemplateFormat(formData);
    const d0ToD8Data = templateData.replacements;
    
    // 构建AI优化请求数据（不包含修改意见）
    const requestData = {
        ...d0ToD8Data
    };
    
    // 如果有修改意见，在外部添加汉字表达
    let requestBody = JSON.stringify(requestData);
    if (requirements && requirements.trim()) {
        requestBody = `修改意见：${requirements.trim()}\n\n表单数据：${requestBody}`;
    }
    
    // 保存requirements到外部作用域
    const optimizeRequirements = requirements;
    
    console.log('AI优化请求数据:', requestData);
    console.log('完整请求内容:', requestBody);
    
    // 发送AI优化请求
    fetch('/submit_8d_report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: requestBody
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    })
    .then(data => {
        if (data.status === 'success') {
            // 如果有AI优化后的数据，创建新版本并回填到表单
            if (data.enhanced_data) {
                console.log('AI优化后的数据:', data.enhanced_data);
                
                // 确保对话管理器和UI已初始化
                if (window.conversationManager && window.conversationUI) {
                    // 确保有当前对话
                    if (!window.conversationUI.currentConversationId) {
                        // 如果没有对话，创建新对话
                        const originalData = collectFormData();
                        const conversationId = window.conversationManager.createConversation(originalData, '8D报告AI协作');
                        window.conversationUI.currentConversationId = conversationId;
                    }
                    
                    // 获取当前对话
                    const conversation = window.conversationManager.conversations.get(window.conversationUI.currentConversationId);
                    if (conversation) {
                        // 添加AI优化版本
                        const customName = optimizeRequirements ? `AI优化：${optimizeRequirements.substring(0, 20)}${optimizeRequirements.length > 20 ? '...' : ''}` : 'AI优化版本';
                        window.conversationManager.addFormVersion(conversation, data.enhanced_data, 'ai', customName, optimizeRequirements);
                        
                        // 显示版本标签栏（如果还没显示）
                        window.conversationUI.showVersionTabs();
                        
                        // 将优化后的数据回填到表单
                        if (window.conversationUI.populateForm) {
                            window.conversationUI.populateForm(data.enhanced_data);
                        } else {
                            // 如果没有populateForm方法，使用简单的回填逻辑
                            fillFormWithData(data.enhanced_data);
                        }
                        
                        alert('AI优化完成！已创建新版本并更新表单内容。');
                        
                        // 更新进度显示
                        updateProgress();
                    }
                } else {
                    // 如果对话系统未初始化，至少回填表单数据
                    fillFormWithData(data.enhanced_data);
                    alert('AI优化完成！表单内容已更新。');
                }
            } else {
                // 没有返回优化数据，但AI处理成功
                if (data.session_id) {
                    // 如果有session_id，提供查看链接选项
                    const viewResult = confirm('AI处理完成，但未返回优化数据。是否要在新窗口查看处理结果？');
                    if (viewResult) {
                        window.open(`/report_preview/${data.session_id}`, '_blank');
                    }
                } else {
                    alert('AI处理完成，但未返回可用的优化数据。请稍后重试或联系管理员。');
                }
            }
        } else {
            throw new Error(data.error || 'AI优化失败');
        }
    })
    .catch(error => {
        console.error('AI优化错误:', error);
        alert('AI优化失败：' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        aiOptimizeBtn.disabled = false;
    });
}

// 简单的表单数据回填函数
function fillFormWithData(data) {
    if (!data || typeof data !== 'object') {
        console.warn('无效的表单数据');
        return;
    }
    
    // 遍历数据对象的所有键值对
    for (const [key, value] of Object.entries(data)) {
        // 尝试找到对应的表单元素
        const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
        
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = !!value;
            } else if (element.type === 'radio') {
                const radioGroup = document.querySelectorAll(`[name="${key}"]`);
                radioGroup.forEach(radio => {
                    radio.checked = radio.value === value;
                });
            } else {
                element.value = value || '';
                
                // 如果是textarea，触发自动调整高度
                if (element.tagName.toLowerCase() === 'textarea') {
                    autoResizeTextarea(element);
                }
            }
        }
    }
    
    console.log('表单数据回填完成');
}

// 直接生成报告
function generateDirectReport() {
    const directGenerateBtn = document.getElementById('direct-generate-btn');
    const btnText = directGenerateBtn.querySelector('.btn-text');
    const btnLoading = directGenerateBtn.querySelector('.btn-loading');
    
    // 显示加载状态
    btnText.style.display = 'none';
    btnLoading.style.display = 'flex';
    directGenerateBtn.disabled = true;
    
    // 收集表单数据
    const formData = collectFormData();
    
    // 调试：显示收集到的表单数据
    console.log('=== 表单数据收集调试 ===');
    console.log('收集到的原始表单数据:', formData);
    console.log('字段数量:', Object.keys(formData).length);
    
    // 显示有值的字段
    const fieldsWithValues = {};
    const emptyFields = {};
    Object.keys(formData).forEach(key => {
        if (formData[key] && formData[key].trim() !== '') {
            fieldsWithValues[key] = formData[key];
        } else {
            emptyFields[key] = formData[key];
        }
    });
    
    console.log('有值的字段 (' + Object.keys(fieldsWithValues).length + '个):', fieldsWithValues);
    console.log('空值字段 (' + Object.keys(emptyFields).length + '个):', Object.keys(emptyFields));
    
    // 检查必填字段是否有值
    const requiredFields = ['d0_background', 'd2_description', 'd2_when', 'd2_where', 'd2_who', 'd2_what', 'd2_why', 'd2_how', 'd2_impact'];
    const missingRequired = requiredFields.filter(field => !formData[field] || formData[field].trim() === '');
    if (missingRequired.length > 0) {
        console.warn('缺少必填字段值:', missingRequired);
        alert('请先填写必填字段：\n' + missingRequired.join('\n'));
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        directGenerateBtn.disabled = false;
        return;
    }
    
    const templateData = convertFormDataToTemplateFormat(formData);
    const d0ToD8Data = templateData.replacements;
    
    console.log('转换后的模板数据:', d0ToD8Data);
    
    // 使用example.json的格式构建请求数据
    const requestData = {
        template_name: "template.pptx",  // 与example.json保持一致
        replacements: d0ToD8Data
    };
    
    console.log('发送到API的数据格式:', requestData);
    
    // 🎯 新增功能：在控制台打印传输给后端的完整JSON数据
    console.log('==========================================');
    console.log('📤 传输给后端的完整JSON数据:');
    console.log('==========================================');
    console.log(JSON.stringify(requestData, null, 2));
    console.log('==========================================');
    
    // 详细检查数据结构
    console.log('=== 数据结构检查 ===');
    console.log('requestData.template_name:', requestData.template_name);
    console.log('requestData.replacements:', requestData.replacements);
    console.log('D0汇报信息:', requestData.replacements['D0汇报信息']);
    console.log('D2问题描述:', requestData.replacements['D2问题描述']);
    
    // 检查关键占位符是否有值
    const importantPlaceholders = [
        '${D0项目背景}', '${D2事件整体描述}', '${D2何时发生}', '${D2何地发生}'
    ];
    console.log('=== 关键占位符检查 ===');
    
    // 扁平化检查（模拟后端的flatten_json_data）
    function flattenForCheck(obj, prefix = '') {
        const flattened = {};
        for (const [key, value] of Object.entries(obj)) {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                Object.assign(flattened, flattenForCheck(value, prefix));
            } else if (key.startsWith('${') && key.endsWith('}')) {
                flattened[key] = value;
            }
        }
        return flattened;
    }
    
    const flattenedData = flattenForCheck(requestData.replacements);
    console.log('扁平化后的数据:', flattenedData);
    
    importantPlaceholders.forEach(placeholder => {
        if (flattenedData[placeholder]) {
            console.log(`✅ ${placeholder}: "${flattenedData[placeholder]}"`);
        } else {
            console.warn(`❌ ${placeholder}: 缺失或为空`);
        }
    });
    
    // 并行调用PPT和DOCX生成接口（参考test_pptx_service.py的方式）
    const pptPromise = fetch('/process_ppt', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    }).then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`PPT生成失败 - HTTP ${response.status}: ${response.statusText}`);
        }
    });
    
    const docxRequestData = {
        template_name: "template.docx",
        replacements: d0ToD8Data
    };
    
    const docxPromise = fetch('/process_docx', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(docxRequestData)
    }).then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`DOCX生成失败 - HTTP ${response.status}: ${response.statusText}`);
        }
    });
    
    // 等待两个请求都完成
    Promise.all([pptPromise, docxPromise])
    .then(([pptResult, docxResult]) => {
        console.log('PPT生成结果:', pptResult);
        console.log('DOCX生成结果:', docxResult);
        
        const files = {};
        if (pptResult && pptResult.status === 'success' && pptResult.download_url) {
            files.ppt = pptResult.download_url;
        }
        if (docxResult && docxResult.status === 'success' && docxResult.download_url) {
            files.docx = docxResult.download_url;
        }
        
        if (Object.keys(files).length > 0) {
            // 显示下载链接的模态框
            showDownloadModal(files);
        } else {
            alert('文件生成失败，请检查表单数据是否完整。');
        }
    })
    .catch(error => {
        console.error('直接生成报告错误:', error);
        alert('报告生成失败：' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        directGenerateBtn.disabled = false;
    });
} 