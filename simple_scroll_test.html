<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单滚动测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: #f8f9fa;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .section {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .section-title {
            padding: 16px;
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 600;
            font-size: 14px;
            color: #333;
            flex-shrink: 0;
        }
        
        .scroll-list {
            flex: 1;
            padding: 8px;
            overflow-y: auto;
            min-height: 0;
        }
        
        .scroll-list::-webkit-scrollbar {
            width: 6px;
        }
        
        .scroll-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .scroll-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .scroll-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .list-item {
            padding: 12px;
            margin-bottom: 4px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .list-item:hover {
            background: #e9ecef;
            transform: translateX(2px);
        }
        
        .list-item.active {
            background: #28a745;
            color: white;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="section">
            <div class="section-title">对话列表</div>
            <div class="scroll-list" id="conversation-list">
                <div class="list-item">对话 1</div>
                <div class="list-item">对话 2</div>
                <div class="list-item">对话 3</div>
                <div class="list-item">对话 4</div>
                <div class="list-item">对话 5</div>
                <div class="list-item">对话 6</div>
                <div class="list-item">对话 7</div>
                <div class="list-item">对话 8</div>
                <div class="list-item">对话 9</div>
                <div class="list-item">对话 10</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">版本记录</div>
            <div class="scroll-list" id="version-list">
                <div class="list-item active">版本 #10</div>
                <div class="list-item">版本 #9</div>
                <div class="list-item">版本 #8</div>
                <div class="list-item">版本 #7</div>
                <div class="list-item">版本 #6</div>
                <div class="list-item">版本 #5</div>
                <div class="list-item">版本 #4</div>
                <div class="list-item">版本 #3</div>
                <div class="list-item">版本 #2</div>
                <div class="list-item">版本 #1</div>
                <div class="list-item">版本 #0</div>
                <div class="list-item">版本 #-1</div>
                <div class="list-item">版本 #-2</div>
                <div class="list-item">版本 #-3</div>
                <div class="list-item">版本 #-4</div>
                <div class="list-item">版本 #-5</div>
            </div>
        </div>
    </div>
    
    <div class="main-content">
        <div class="test-info">
            <h2>简单滚动测试</h2>
            <p>这是一个简化的测试页面，用于验证基本的滚动功能。</p>
            <h3>测试说明：</h3>
            <ul>
                <li>左侧侧边栏分为两个区域：对话列表和版本记录</li>
                <li>每个区域都应该有独立的滚动条</li>
                <li>鼠标悬停在对应区域时，滚轮应该能够滚动该区域的内容</li>
                <li>滚动条应该在内容超出容器时自动显示</li>
            </ul>
            <h3>预期行为：</h3>
            <ul>
                <li>两个列表区域都应该显示滚动条</li>
                <li>滚动条宽度为6px，颜色为灰色</li>
                <li>鼠标悬停时滚动条颜色变深</li>
                <li>滚轮在对应区域内应该能正常工作</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的滚轮控制
        document.addEventListener('wheel', function(e) {
            const target = e.target;
            const sidebar = document.querySelector('.sidebar');
            
            if (sidebar && sidebar.contains(target)) {
                // 找到最近的可滚动元素
                let scrollableElement = target;
                while (scrollableElement && scrollableElement !== sidebar) {
                    const computedStyle = window.getComputedStyle(scrollableElement);
                    const overflowY = computedStyle.overflowY;
                    
                    if (overflowY === 'auto' || overflowY === 'scroll') {
                        break;
                    }
                    scrollableElement = scrollableElement.parentElement;
                }
                
                if (scrollableElement && scrollableElement !== sidebar) {
                    // 检查是否需要滚动
                    const needsScroll = scrollableElement.scrollHeight > scrollableElement.clientHeight;
                    
                    if (needsScroll) {
                        // 阻止事件冒泡到主页面
                        e.stopPropagation();
                        
                        // 检查滚动边界
                        const scrollTop = scrollableElement.scrollTop;
                        const scrollHeight = scrollableElement.scrollHeight;
                        const clientHeight = scrollableElement.clientHeight;
                        
                        const isAtTop = scrollTop === 0;
                        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;
                        
                        // 如果在边界且滚动方向会导致溢出，则阻止默认行为
                        if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
                            e.preventDefault();
                        }
                        
                        return;
                    }
                }
            }
        }, { passive: false });
    </script>
</body>
</html>
