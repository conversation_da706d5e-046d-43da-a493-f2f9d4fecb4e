/**
 * 验证版本记录滚动修复的脚本
 * 在浏览器控制台中运行此脚本来检查修复效果
 */

function verifyScrollFix() {
    console.log('🔍 开始验证版本记录滚动修复...');
    
    // 1. 检查版本列表元素是否存在
    const versionList = document.getElementById('version-list');
    if (!versionList) {
        console.error('❌ 版本列表元素未找到');
        return false;
    }
    console.log('✅ 版本列表元素存在');
    
    // 2. 检查版本列表的CSS样式
    const computedStyle = window.getComputedStyle(versionList);
    console.log('📊 版本列表样式信息:');
    console.log('  - overflow-y:', computedStyle.overflowY);
    console.log('  - flex:', computedStyle.flex);
    console.log('  - min-height:', computedStyle.minHeight);
    console.log('  - max-height:', computedStyle.maxHeight);
    console.log('  - padding:', computedStyle.padding);
    
    // 3. 检查滚动条样式
    console.log('🎨 滚动条样式检查:');
    const scrollbarWidth = computedStyle.getPropertyValue('scrollbar-width');
    console.log('  - scrollbar-width (Firefox):', scrollbarWidth || '未设置');
    
    // 4. 检查版本内容区域
    const versionContent = versionList.closest('.version-content');
    if (versionContent) {
        const contentStyle = window.getComputedStyle(versionContent);
        console.log('📦 版本内容区域样式:');
        console.log('  - overflow:', contentStyle.overflow);
        console.log('  - display:', contentStyle.display);
        console.log('  - flex-direction:', contentStyle.flexDirection);
    }
    
    // 5. 检查滚动能力
    console.log('📏 滚动能力检查:');
    console.log('  - scrollHeight:', versionList.scrollHeight);
    console.log('  - clientHeight:', versionList.clientHeight);
    console.log('  - 需要滚动:', versionList.scrollHeight > versionList.clientHeight);
    
    // 6. 检查版本项数量
    const versionItems = versionList.querySelectorAll('.version-item');
    console.log('📋 版本项信息:');
    console.log('  - 版本项数量:', versionItems.length);
    
    // 7. 测试滚轮事件绑定
    console.log('🖱️ 滚轮事件检查:');
    const hasWheelListener = typeof initWheelControl === 'function';
    console.log('  - initWheelControl函数存在:', hasWheelListener);
    
    // 8. 检查侧边栏容器
    const sidebar = document.getElementById('unified-sidebar');
    if (sidebar) {
        const sidebarStyle = window.getComputedStyle(sidebar);
        console.log('🏠 侧边栏容器样式:');
        console.log('  - overflow:', sidebarStyle.overflow);
        console.log('  - height:', sidebarStyle.height);
        console.log('  - display:', sidebarStyle.display);
    }
    
    // 9. 生成修复状态报告
    const isScrollable = versionList.scrollHeight > versionList.clientHeight;
    const hasCorrectOverflow = computedStyle.overflowY === 'auto' || computedStyle.overflowY === 'scroll';
    const hasFlexLayout = computedStyle.flex !== 'none';
    
    console.log('\n📋 修复状态报告:');
    console.log('  ✅ 版本列表可滚动:', isScrollable ? '是' : '否');
    console.log('  ✅ 正确的overflow设置:', hasCorrectOverflow ? '是' : '否');
    console.log('  ✅ 正确的flex布局:', hasFlexLayout ? '是' : '否');
    console.log('  ✅ 滚轮事件支持:', hasWheelListener ? '是' : '否');
    
    const allGood = isScrollable && hasCorrectOverflow && hasFlexLayout && hasWheelListener;
    console.log('\n🎯 总体状态:', allGood ? '✅ 修复成功' : '❌ 需要进一步调试');
    
    return allGood;
}

// 添加一些测试版本项的函数
function addTestVersions(count = 10) {
    const versionList = document.getElementById('version-list');
    if (!versionList) {
        console.error('版本列表未找到');
        return;
    }
    
    // 清空现有内容
    versionList.innerHTML = '';
    
    // 添加测试版本项
    for (let i = count; i >= 1; i--) {
        const versionItem = document.createElement('div');
        versionItem.className = 'version-item' + (i === count ? ' active' : '');
        versionItem.innerHTML = `
            <div class="version-icon">${i % 2 === 0 ? '🤖' : '👤'}</div>
            <div class="version-info">
                <div class="version-name">测试版本 #${i}</div>
                <div class="version-meta">07/23 ${String(10 + i).padStart(2, '0')}:00</div>
            </div>
            <button class="version-delete-btn">×</button>
        `;
        versionList.appendChild(versionItem);
    }
    
    console.log(`✅ 已添加 ${count} 个测试版本项`);
    
    // 重新验证滚动
    setTimeout(() => {
        verifyScrollFix();
    }, 100);
}

// 测试滚动到特定位置
function testScrollTo(position) {
    const versionList = document.getElementById('version-list');
    if (!versionList) {
        console.error('版本列表未找到');
        return;
    }
    
    versionList.scrollTop = position;
    console.log(`📍 滚动到位置: ${position}px`);
    console.log(`📍 当前滚动位置: ${versionList.scrollTop}px`);
}

// 导出函数到全局作用域
window.verifyScrollFix = verifyScrollFix;
window.addTestVersions = addTestVersions;
window.testScrollTo = testScrollTo;

console.log('🚀 版本记录滚动验证脚本已加载');
console.log('💡 使用方法:');
console.log('  - verifyScrollFix() - 验证修复状态');
console.log('  - addTestVersions(10) - 添加10个测试版本项');
console.log('  - testScrollTo(100) - 滚动到指定位置');
