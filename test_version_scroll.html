<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本记录滚动测试</title>
    <link rel="stylesheet" href="static/css/conversation_styles.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            display: flex;
            height: 100vh;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background: white;
            margin-left: 280px;
        }
        
        /* 确保版本列表有足够的内容来测试滚动 */
        .version-item {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- 侧边栏 -->
        <div class="unified-sidebar" id="unified-sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <h3 style="margin: 0; color: #333;">测试侧边栏</h3>
            </div>

            <!-- 新建对话按钮 -->
            <div class="new-conversation-section">
                <button class="new-conversation-btn">
                    <span class="btn-icon">+</span>
                    <span class="btn-text">新对话</span>
                </button>
            </div>

            <!-- 对话历史列表 -->
            <div class="conversation-history-section">
                <div class="section-title">历史对话</div>
                <div class="conversation-list" id="conversation-list">
                    <div class="conversation-item">
                        <div class="conversation-icon">💬</div>
                        <div class="conversation-content">
                            <div class="conversation-title">测试对话 1</div>
                            <div class="conversation-timestamp">07/23 14:30</div>
                        </div>
                    </div>
                    <div class="conversation-item active">
                        <div class="conversation-icon">💬</div>
                        <div class="conversation-content">
                            <div class="conversation-title">当前对话</div>
                            <div class="conversation-timestamp">07/23 15:00</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 版本记录区域 -->
            <div class="version-history-section" id="version-history-section">
                <div class="section-title">版本记录</div>
                <div class="version-content">
                    <div class="version-actions">
                        <button class="add-version-btn">
                            <span class="btn-icon">+</span>
                            <span class="btn-text">新增版本</span>
                        </button>
                    </div>
                    <div class="version-list" id="version-list">
                        <!-- 添加多个版本项来测试滚动 -->
                        <div class="version-item active">
                            <div class="version-icon">🤖</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #10</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #9</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #8</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #7</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #6</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #5</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #4</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #3</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #2</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                        <div class="version-item">
                            <div class="version-icon">👤</div>
                            <div class="version-info">
                                <div class="version-name">空白版本 #1</div>
                                <div class="version-meta">07/23 17:20</div>
                            </div>
                            <button class="version-delete-btn">×</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <h1>版本记录滚动测试</h1>
            <p>请在左侧版本记录区域测试以下功能：</p>
            <ul>
                <li>✅ 滚动条是否显示</li>
                <li>✅ 鼠标悬停在版本列表上时，滚轮是否可以滚动</li>
                <li>✅ 滚动条是否足够明显（宽度6px）</li>
                <li>✅ 版本列表是否可以正常滚动到底部和顶部</li>
            </ul>
            
            <h2>修复内容：</h2>
            <ol>
                <li>✅ 移除了 <code>version-content</code> 的 <code>overflow: hidden</code> 属性</li>
                <li>✅ 增加了滚动条宽度从4px到6px，使其更明显</li>
                <li>✅ 添加了Firefox浏览器的滚动条样式支持</li>
                <li>✅ 为版本列表设置了最大高度限制</li>
                <li>✅ 确保滚动条有最小高度显示</li>
                <li>✅ 优化了版本列表的padding，为滚动条预留空间</li>
                <li>✅ 集成了现有的滚轮事件处理机制</li>
            </ol>

            <h2>技术细节：</h2>
            <ul>
                <li><strong>滚动条样式</strong>：WebKit浏览器使用6px宽度，Firefox使用thin样式</li>
                <li><strong>滚轮事件</strong>：通过 <code>initWheelControl()</code> 函数处理，支持版本列表区域的滚轮滚动</li>
                <li><strong>布局优化</strong>：使用flexbox布局确保版本列表占据剩余空间</li>
                <li><strong>兼容性</strong>：支持Chrome、Firefox、Safari等主流浏览器</li>
            </ul>
        </div>
    </div>

    <script src="static/js/d8_form.js"></script>
    <script>
        // 初始化滚轮控制
        if (typeof initWheelControl === 'function') {
            initWheelControl();
        }
        
        // 测试滚动功能
        document.addEventListener('DOMContentLoaded', function() {
            const versionList = document.getElementById('version-list');
            if (versionList) {
                console.log('版本列表元素:', versionList);
                console.log('滚动高度:', versionList.scrollHeight);
                console.log('客户端高度:', versionList.clientHeight);
                console.log('是否需要滚动:', versionList.scrollHeight > versionList.clientHeight);
            }
        });
    </script>
</body>
</html>
